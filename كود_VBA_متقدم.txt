كود VBA للوظائف المتقدمة في نظام إدارة المصنع

1. دالة حساب المواد المطلوبة للإنتاج:
```vba
Public Function CalculateRequiredMaterials(ProductionOrderID As Long) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    
    Set db = CurrentDb
    
    ' الحصول على تفاصيل أمر الإنتاج
    strSQL = "SELECT po.ProductID, po.RecipeID, po.PlannedQuantity " & _
             "FROM ProductionOrders po WHERE po.ProductionOrderID = " & ProductionOrderID
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        Dim ProductID As Long, RecipeID As Long, PlannedQuantity As Double
        ProductID = rs!ProductID
        RecipeID = rs!RecipeID
        PlannedQuantity = rs!PlannedQuantity
        
        ' حساب المواد المطلوبة من الوصفة
        strSQL = "SELECT ri.MaterialID, ri.Quantity, r.BatchSize " & _
                 "FROM RecipeIngredients ri INNER JOIN Recipes r ON ri.RecipeID = r.RecipeID " & _
                 "WHERE ri.RecipeID = " & RecipeID
        Set rs = db.OpenRecordset(strSQL)
        
        While Not rs.EOF
            Dim RequiredQuantity As Double
            RequiredQuantity = (rs!Quantity / rs!BatchSize) * PlannedQuantity
            
            ' التحقق من توفر المادة في المخزون
            Dim CurrentStock As Double
            CurrentStock = DLookup("CurrentStock", "RawMaterials", "MaterialID = " & rs!MaterialID)
            
            If CurrentStock < RequiredQuantity Then
                MsgBox "المادة " & DLookup("MaterialName", "RawMaterials", "MaterialID = " & rs!MaterialID) & _
                       " غير متوفرة بالكمية المطلوبة. المتوفر: " & CurrentStock & _
                       " المطلوب: " & RequiredQuantity
                CalculateRequiredMaterials = False
                Exit Function
            End If
            
            rs.MoveNext
        Wend
        
        CalculateRequiredMaterials = True
    Else
        CalculateRequiredMaterials = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function
```

2. دالة تحديث المخزون عند الإنتاج:
```vba
Public Function UpdateInventoryForProduction(ProductionOrderID As Long) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    
    Set db = CurrentDb
    db.BeginTrans
    
    On Error GoTo ErrorHandler
    
    ' الحصول على تفاصيل أمر الإنتاج
    strSQL = "SELECT po.ProductID, po.RecipeID, po.ProducedQuantity " & _
             "FROM ProductionOrders po WHERE po.ProductionOrderID = " & ProductionOrderID
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        Dim ProductID As Long, RecipeID As Long, ProducedQuantity As Double
        ProductID = rs!ProductID
        RecipeID = rs!RecipeID
        ProducedQuantity = rs!ProducedQuantity
        
        ' صرف المواد الخام
        strSQL = "SELECT ri.MaterialID, ri.Quantity, r.BatchSize " & _
                 "FROM RecipeIngredients ri INNER JOIN Recipes r ON ri.RecipeID = r.RecipeID " & _
                 "WHERE ri.RecipeID = " & RecipeID
        Set rs = db.OpenRecordset(strSQL)
        
        While Not rs.EOF
            Dim UsedQuantity As Double
            UsedQuantity = (rs!Quantity / rs!BatchSize) * ProducedQuantity
            
            ' تحديث مخزون المادة الخام
            db.Execute "UPDATE RawMaterials SET CurrentStock = CurrentStock - " & UsedQuantity & _
                      " WHERE MaterialID = " & rs!MaterialID
            
            ' تسجيل حركة المخزون
            db.Execute "INSERT INTO InventoryMovements (MovementDate, MovementType, ItemType, ItemID, " & _
                      "Quantity, ReferenceType, ReferenceID, CreatedBy) VALUES " & _
                      "(Now(), 'Out', 'RawMaterial', " & rs!MaterialID & ", " & UsedQuantity & _
                      ", 'Production', " & ProductionOrderID & ", 'النظام')"
            
            rs.MoveNext
        Wend
        
        ' إضافة المنتج النهائي للمخزون
        db.Execute "UPDATE Products SET CurrentStock = CurrentStock + " & ProducedQuantity & _
                  " WHERE ProductID = " & ProductID
        
        ' تسجيل حركة المخزون للمنتج النهائي
        db.Execute "INSERT INTO InventoryMovements (MovementDate, MovementType, ItemType, ItemID, " & _
                  "Quantity, ReferenceType, ReferenceID, CreatedBy) VALUES " & _
                  "(Now(), 'In', 'Product', " & ProductID & ", " & ProducedQuantity & _
                  ", 'Production', " & ProductionOrderID & ", 'النظام')"
        
        db.CommitTrans
        UpdateInventoryForProduction = True
    Else
        db.Rollback
        UpdateInventoryForProduction = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    db.Rollback
    UpdateInventoryForProduction = False
    MsgBox "حدث خطأ أثناء تحديث المخزون: " & Err.Description
End Function
```

3. دالة تحديث المخزون عند البيع:
```vba
Public Function UpdateInventoryForSale(SalesInvoiceID As Long) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    
    Set db = CurrentDb
    db.BeginTrans
    
    On Error GoTo ErrorHandler
    
    ' الحصول على تفاصيل فاتورة المبيعات
    strSQL = "SELECT sid.ProductID, sid.Quantity " & _
             "FROM SalesInvoiceDetails sid WHERE sid.InvoiceID = " & SalesInvoiceID
    Set rs = db.OpenRecordset(strSQL)
    
    While Not rs.EOF
        ' التحقق من توفر المنتج في المخزون
        Dim CurrentStock As Double
        CurrentStock = DLookup("CurrentStock", "Products", "ProductID = " & rs!ProductID)
        
        If CurrentStock < rs!Quantity Then
            MsgBox "المنتج " & DLookup("ProductName", "Products", "ProductID = " & rs!ProductID) & _
                   " غير متوفر بالكمية المطلوبة. المتوفر: " & CurrentStock & _
                   " المطلوب: " & rs!Quantity
            db.Rollback
            UpdateInventoryForSale = False
            Exit Function
        End If
        
        ' تحديث مخزون المنتج
        db.Execute "UPDATE Products SET CurrentStock = CurrentStock - " & rs!Quantity & _
                  " WHERE ProductID = " & rs!ProductID
        
        ' تسجيل حركة المخزون
        db.Execute "INSERT INTO InventoryMovements (MovementDate, MovementType, ItemType, ItemID, " & _
                  "Quantity, ReferenceType, ReferenceID, CreatedBy) VALUES " & _
                  "(Now(), 'Out', 'Product', " & rs!ProductID & ", " & rs!Quantity & _
                  ", 'Sale', " & SalesInvoiceID & ", 'النظام')"
        
        rs.MoveNext
    Wend
    
    db.CommitTrans
    UpdateInventoryForSale = True
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    db.Rollback
    UpdateInventoryForSale = False
    MsgBox "حدث خطأ أثناء تحديث المخزون: " & Err.Description
End Function
```

4. دالة إنشاء رقم تلقائي للطلبات:
```vba
Public Function GenerateOrderNumber(OrderType As String) As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    Dim NextNumber As Long
    Dim Prefix As String
    
    Set db = CurrentDb
    
    ' تحديد البادئة حسب نوع الطلب
    Select Case OrderType
        Case "Purchase"
            Prefix = "PO-"
        Case "Production"
            Prefix = "PROD-"
        Case "Sales"
            Prefix = "INV-"
        Case Else
            Prefix = "ORD-"
    End Select
    
    ' الحصول على آخر رقم مستخدم
    strSQL = "SELECT SettingValue FROM Settings WHERE SettingKey = 'LastOrderNumber_" & OrderType & "'"
    Set rs = db.OpenRecordset(strSQL)
    
    If rs.EOF Then
        ' إنشاء إعداد جديد إذا لم يكن موجوداً
        NextNumber = 1
        db.Execute "INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES " & _
                  "('LastOrderNumber_" & OrderType & "', '1', 'آخر رقم طلب لنوع " & OrderType & "')"
    Else
        NextNumber = Val(rs!SettingValue) + 1
        db.Execute "UPDATE Settings SET SettingValue = '" & NextNumber & "' " & _
                  "WHERE SettingKey = 'LastOrderNumber_" & OrderType & "'"
    End If
    
    GenerateOrderNumber = Prefix & Year(Date) & "-" & Format(NextNumber, "000")
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function
```

5. دالة التحقق من مستويات المخزون المنخفض:
```vba
Public Function CheckLowStockLevels() As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    Dim LowStockItems As String
    
    Set db = CurrentDb
    
    ' التحقق من المواد الخام
    strSQL = "SELECT MaterialName, CurrentStock, MinStockLevel " & _
             "FROM RawMaterials WHERE CurrentStock <= MinStockLevel AND IsActive = True"
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        LowStockItems = "المواد الخام منخفضة المخزون:" & vbCrLf
        While Not rs.EOF
            LowStockItems = LowStockItems & "- " & rs!MaterialName & _
                           " (الحالي: " & rs!CurrentStock & ", الحد الأدنى: " & rs!MinStockLevel & ")" & vbCrLf
            rs.MoveNext
        Wend
        LowStockItems = LowStockItems & vbCrLf
    End If
    
    ' التحقق من المنتجات النهائية
    strSQL = "SELECT ProductName, CurrentStock, MinStockLevel " & _
             "FROM Products WHERE CurrentStock <= MinStockLevel AND IsActive = True"
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        LowStockItems = LowStockItems & "المنتجات النهائية منخفضة المخزون:" & vbCrLf
        While Not rs.EOF
            LowStockItems = LowStockItems & "- " & rs!ProductName & _
                           " (الحالي: " & rs!CurrentStock & ", الحد الأدنى: " & rs!MinStockLevel & ")" & vbCrLf
            rs.MoveNext
        Wend
    End If
    
    CheckLowStockLevels = LowStockItems
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function
```

6. دالة حساب تكلفة الوصفة:
```vba
Public Function CalculateRecipeCost(RecipeID As Long) As Double
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim strSQL As String
    Dim TotalCost As Double
    
    Set db = CurrentDb
    
    strSQL = "SELECT ri.Quantity, rm.UnitCost " & _
             "FROM RecipeIngredients ri INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID " & _
             "WHERE ri.RecipeID = " & RecipeID
    Set rs = db.OpenRecordset(strSQL)
    
    TotalCost = 0
    While Not rs.EOF
        TotalCost = TotalCost + (rs!Quantity * rs!UnitCost)
        rs.MoveNext
    Wend
    
    CalculateRecipeCost = TotalCost
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function
```

استخدام هذه الدوال:
1. إضافة الكود في وحدة VBA جديدة
2. استدعاء الدوال من النماذج والتقارير
3. ربط الدوال بأحداث النماذج (مثل عند الحفظ)
4. استخدام الدوال في الاستعلامات المحسوبة
