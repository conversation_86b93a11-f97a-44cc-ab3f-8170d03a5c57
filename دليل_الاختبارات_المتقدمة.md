# دليل الاختبارات المتقدمة لنظام إدارة المصنع

## نظرة عامة
هذا الدليل يحتوي على مجموعة شاملة من الاختبارات العملية المتقدمة لضمان جودة وموثوقية نظام إدارة مصنع المواد الغذائية.

## فهرس الاختبارات

1. [اختبارات الجداول والبيانات](#1-اختبارات-الجداول-والبيانات)
2. [اختبارات العلاقات بين الجداول](#2-اختبارات-العلاقات-بين-الجداول)
3. [اختبارات الاستعلامات المتقدمة](#3-اختبارات-الاستعلامات-المتقدمة)
4. [اختبارات السيناريوهات العملية](#4-اختبارات-السيناريوهات-العملية)
5. [اختبارات الضغط والأداء](#5-اختبارات-الضغط-والأداء)
6. [اختبارات حالات الخطأ](#6-اختبارات-حالات-الخطأ)

---

## 1. اختبارات الجداول والبيانات

### 1.1 اختبار سلامة البيانات الأساسية

#### الهدف: التأكد من صحة البيانات في جميع الجداول الـ20

#### الاختبار 1.1.1: فحص وجود الجداول
```sql
-- خطوات التنفيذ:
SELECT COUNT(*) AS [عدد الجداول] 
FROM MSysObjects 
WHERE Type = 1 AND Name NOT LIKE 'MSys*';

-- النتيجة المتوقعة: 20 جدول
-- معايير النجاح: العدد = 20
```

#### الاختبار 1.1.2: فحص البيانات الأساسية
```sql
-- اختبار وحدات القياس
SELECT COUNT(*) AS [عدد الوحدات] FROM Units;
-- النتيجة المتوقعة: 7 وحدات

-- اختبار الفئات
SELECT COUNT(*) AS [عدد الفئات] FROM Categories;
-- النتيجة المتوقعة: 9 فئات

-- اختبار مراحل الإنتاج
SELECT COUNT(*) AS [عدد المراحل] FROM ProductionStages;
-- النتيجة المتوقعة: 8 مراحل

-- اختبار الإعدادات
SELECT COUNT(*) AS [عدد الإعدادات] FROM Settings;
-- النتيجة المتوقعة: 8 إعدادات
```

### 1.2 اختبار القيود والمفاتيح

#### الاختبار 1.2.1: اختبار المفاتيح الأساسية
```sql
-- خطوات التنفيذ:
-- 1. محاولة إدخال مفتاح أساسي مكرر
INSERT INTO Units (UnitID, UnitName) VALUES (1, 'وحدة مكررة');

-- النتيجة المتوقعة: خطأ - انتهاك المفتاح الأساسي
-- معايير النجاح: رفض الإدراج مع رسالة خطأ واضحة
```

#### الاختبار 1.2.2: اختبار الحقول المطلوبة (NOT NULL)
```sql
-- خطوات التنفيذ:
-- 1. محاولة إدخال قيمة فارغة في حقل مطلوب
INSERT INTO Suppliers (SupplierName, Phone) VALUES (NULL, '123456789');

-- النتيجة المتوقعة: خطأ - الحقل مطلوب
-- معايير النجاح: رفض الإدراج مع رسالة خطأ
```

#### الاختبار 1.2.3: اختبار أنواع البيانات
```sql
-- خطوات التنفيذ:
-- 1. محاولة إدخال نص في حقل رقمي
INSERT INTO RawMaterials (MaterialName, UnitCost) VALUES ('مادة اختبار', 'نص بدلاً من رقم');

-- النتيجة المتوقعة: خطأ - نوع البيانات غير صحيح
-- معايير النجاح: رفض الإدراج مع رسالة خطأ
```

### 1.3 اختبار حدود البيانات

#### الاختبار 1.3.1: اختبار الحدود الرقمية
```sql
-- خطوات التنفيذ:
-- 1. إدخال قيم سالبة في حقول لا تقبل السالب
INSERT INTO RawMaterials (MaterialName, CurrentStock, UnitCost) 
VALUES ('مادة اختبار', -10, -5.50);

-- النتيجة المتوقعة: قبول أو رفض حسب قواعد العمل
-- معايير النجاح: سلوك متسق مع المتطلبات
```

#### الاختبار 1.3.2: اختبار طول النصوص
```sql
-- خطوات التنفيذ:
-- 1. إدخال نص طويل جداً
INSERT INTO Suppliers (SupplierName) 
VALUES (REPLICATE('أ', 300)); -- نص من 300 حرف

-- النتيجة المتوقعة: قطع النص أو رفض الإدراج
-- معايير النجاح: عدم كسر النظام
```

---

## 2. اختبارات العلاقات بين الجداول

### 2.1 اختبار العلاقات الموجودة

#### الاختبار 2.1.1: فحص وجود العلاقات
```sql
-- خطوات التنفيذ:
-- في Access: Database Tools > Relationships
-- فحص بصري للعلاقات

-- النتيجة المتوقعة: 18 علاقة مرئية
-- معايير النجاح: جميع الجداول مترابطة بشكل صحيح
```

#### الاختبار 2.1.2: اختبار سلامة المراجع
```sql
-- خطوات التنفيذ:
-- 1. محاولة إدخال مفتاح خارجي غير موجود
INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID) 
VALUES ('مادة اختبار', 999, 999);

-- النتيجة المتوقعة: خطأ - المفتاح الخارجي غير موجود
-- معايير النجاح: رفض الإدراج مع رسالة خطأ واضحة
```

### 2.2 اختبار الحذف والتحديث المتتالي

#### الاختبار 2.2.1: اختبار منع الحذف
```sql
-- خطوات التنفيذ:
-- 1. محاولة حذف مورد له مواد خام مرتبطة
DELETE FROM Suppliers WHERE SupplierID = 1;

-- النتيجة المتوقعة: خطأ - لا يمكن حذف السجل المرتبط
-- معايير النجاح: منع الحذف مع رسالة خطأ واضحة
```

#### الاختبار 2.2.2: اختبار التحديث المتتالي
```sql
-- خطوات التنفيذ:
-- 1. تحديث مفتاح أساسي والتحقق من تحديث المفاتيح الخارجية
-- (يتطلب تفعيل Cascade Update في العلاقات)

-- النتيجة المتوقعة: تحديث جميع المراجع
-- معايير النجاح: تحديث متسق في جميع الجداول المرتبطة
```

---

## 3. اختبارات الاستعلامات المتقدمة

### 3.1 اختبار الاستعلامات الأساسية

#### الاختبار 3.1.1: اختبار استعلام تفاصيل المواد الخام
```sql
-- خطوات التنفيذ:
SELECT COUNT(*) FROM vw_RawMaterialsDetails;

-- النتيجة المتوقعة: عدد يساوي عدد المواد الخام النشطة
-- معايير النجاح: لا توجد أخطاء في التنفيذ
```

#### الاختبار 3.1.2: اختبار استعلام تكلفة الوصفات
```sql
-- خطوات التنفيذ:
SELECT * FROM vw_RecipeTotalCost WHERE RecipeID = 1;

-- النتيجة المتوقعة: تكلفة محسوبة بدقة
-- معايير النجاح: التكلفة = مجموع (الكمية × تكلفة الوحدة) لجميع المكونات
```

### 3.2 اختبار الاستعلامات مع بيانات متنوعة

#### الاختبار 3.2.1: اختبار مع بيانات فارغة
```sql
-- خطوات التنفيذ:
-- 1. إنشاء مادة خام بدون مورد
INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, SupplierID) 
VALUES ('مادة بدون مورد', 1, 1, NULL);

-- 2. تشغيل استعلام تفاصيل المواد الخام
SELECT * FROM vw_RawMaterialsDetails WHERE MaterialName = 'مادة بدون مورد';

-- النتيجة المتوقعة: عرض السجل مع قيم فارغة للمورد
-- معايير النجاح: عدم كسر الاستعلام
```

### 3.3 اختبار دقة الحسابات

#### الاختبار 3.3.1: اختبار حساب قيمة المخزون
```sql
-- خطوات التنفيذ:
-- 1. حساب قيمة المخزون يدوياً لمادة واحدة
-- 2. مقارنة النتيجة مع الاستعلام

SELECT 
    MaterialName,
    CurrentStock,
    UnitCost,
    (CurrentStock * UnitCost) AS [قيمة المخزون المحسوبة]
FROM vw_RawMaterialsDetails 
WHERE MaterialID = 1;

-- معايير النجاح: تطابق الحساب اليدوي مع نتيجة الاستعلام
```

---

## 4. اختبارات السيناريوهات العملية

### 4.1 سيناريو دورة الشراء الكاملة

#### الاختبار 4.1.1: إنشاء طلب شراء جديد
```sql
-- خطوات التنفيذ:
-- 1. إنشاء طلب شراء جديد
INSERT INTO PurchaseOrders (OrderNumber, SupplierID, OrderDate, Status) 
VALUES ('PO-TEST-001', 1, Date(), 'Pending');

-- 2. الحصول على ID الطلب الجديد
DECLARE @OrderID INT = @@IDENTITY;

-- 3. إضافة تفاصيل الطلب
INSERT INTO PurchaseOrderDetails (OrderID, MaterialID, Quantity, UnitPrice) 
VALUES (@OrderID, 1, 100, 5.50);

-- النتيجة المتوقعة: إنشاء الطلب وتفاصيله بنجاح
-- معايير النجاح: ظهور الطلب في استعلام vw_PurchaseOrdersDetails
```

#### الاختبار 4.1.2: تحويل طلب الشراء إلى فاتورة
```sql
-- خطوات التنفيذ:
-- 1. إنشاء فاتورة شراء مرتبطة بالطلب
INSERT INTO PurchaseInvoices (InvoiceNumber, SupplierID, OrderID, InvoiceDate) 
VALUES ('PI-TEST-001', 1, @OrderID, Date());

-- 2. إضافة تفاصيل الفاتورة
DECLARE @InvoiceID INT = @@IDENTITY;
INSERT INTO PurchaseInvoiceDetails (InvoiceID, MaterialID, Quantity, UnitPrice) 
VALUES (@InvoiceID, 1, 100, 5.50);

-- 3. تحديث المخزون
UPDATE RawMaterials 
SET CurrentStock = CurrentStock + 100 
WHERE MaterialID = 1;

-- معايير النجاح: زيادة المخزون بالكمية المستلمة
```

### 4.2 سيناريو دورة الإنتاج الكاملة

#### الاختبار 4.2.1: إنشاء أمر إنتاج
```sql
-- خطوات التنفيذ:
-- 1. التحقق من توفر المواد الخام للوصفة
SELECT 
    ri.MaterialID,
    ri.Quantity AS [مطلوب],
    rm.CurrentStock AS [متوفر],
    CASE 
        WHEN rm.CurrentStock >= ri.Quantity THEN 'متوفر'
        ELSE 'غير متوفر'
    END AS [الحالة]
FROM RecipeIngredients ri
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
WHERE ri.RecipeID = 1;

-- 2. إنشاء أمر الإنتاج إذا كانت المواد متوفرة
INSERT INTO ProductionOrders (OrderNumber, ProductID, RecipeID, PlannedQuantity, StartDate, Status) 
VALUES ('PROD-TEST-001', 1, 1, 50, Date(), 'InProgress');

-- معايير النجاح: إنشاء أمر الإنتاج بنجاح
```

#### الاختبار 4.2.2: تنفيذ أمر الإنتاج
```sql
-- خطوات التنفيذ:
-- 1. صرف المواد الخام من المخزون
DECLARE @ProductionOrderID INT = @@IDENTITY;
DECLARE @PlannedQty INT = 50;

-- صرف المواد حسب الوصفة
UPDATE rm 
SET CurrentStock = CurrentStock - (ri.Quantity * @PlannedQty)
FROM RawMaterials rm
INNER JOIN RecipeIngredients ri ON rm.MaterialID = ri.MaterialID
WHERE ri.RecipeID = 1;

-- 2. إضافة المنتج النهائي للمخزون
UPDATE Products 
SET CurrentStock = CurrentStock + @PlannedQty 
WHERE ProductID = 1;

-- 3. تحديث حالة أمر الإنتاج
UPDATE ProductionOrders 
SET Status = 'Completed', ActualQuantity = @PlannedQty, EndDate = Date()
WHERE ProductionOrderID = @ProductionOrderID;

-- معايير النجاح: تحديث المخزون بدقة
```

### 4.3 سيناريو دورة المبيعات الكاملة

#### الاختبار 4.3.1: إنشاء فاتورة مبيعات
```sql
-- خطوات التنفيذ:
-- 1. إنشاء فاتورة مبيعات
INSERT INTO SalesInvoices (InvoiceNumber, CustomerID, InvoiceDate, TaxRate) 
VALUES ('SI-TEST-001', 1, Date(), 15);

-- 2. إضافة تفاصيل الفاتورة
DECLARE @SalesInvoiceID INT = @@IDENTITY;
INSERT INTO SalesInvoiceDetails (InvoiceID, ProductID, Quantity, UnitPrice) 
VALUES (@SalesInvoiceID, 1, 20, 25.00);

-- 3. حساب المجاميع
DECLARE @SubTotal DECIMAL(10,2) = 20 * 25.00;
DECLARE @TaxAmount DECIMAL(10,2) = @SubTotal * 0.15;
DECLARE @NetAmount DECIMAL(10,2) = @SubTotal + @TaxAmount;

UPDATE SalesInvoices 
SET SubTotal = @SubTotal, TaxAmount = @TaxAmount, NetAmount = @NetAmount
WHERE InvoiceID = @SalesInvoiceID;

-- معايير النجاح: حساب المجاميع بدقة
```

#### الاختبار 4.3.2: صرف المنتجات من المخزون
```sql
-- خطوات التنفيذ:
-- 1. التحقق من توفر المنتج
SELECT CurrentStock FROM Products WHERE ProductID = 1;

-- 2. صرف المنتج من المخزون
UPDATE Products 
SET CurrentStock = CurrentStock - 20 
WHERE ProductID = 1;

-- 3. تسجيل حركة المخزون
INSERT INTO InventoryMovements (MovementDate, MovementType, ProductID, Quantity, ReferenceType, ReferenceID)
VALUES (Date(), 'Out', 1, 20, 'Sales', @SalesInvoiceID);

-- معايير النجاح: تقليل المخزون بالكمية المباعة
```

---

## 5. اختبارات الضغط والأداء

### 5.1 اختبار البيانات الكبيرة

#### الاختبار 5.1.1: إدراج 1000 سجل
```sql
-- خطوات التنفيذ:
-- 1. إنشاء حلقة لإدراج 1000 مورد
DECLARE @i INT = 1;
WHILE @i <= 1000
BEGIN
    INSERT INTO Suppliers (SupplierName, Phone, Email) 
    VALUES ('مورد اختبار ' + CAST(@i AS VARCHAR), '123456' + CAST(@i AS VARCHAR), 'test' + CAST(@i AS VARCHAR) + '@test.com');
    SET @i = @i + 1;
END

-- النتيجة المتوقعة: إدراج جميع السجلات بنجاح
-- معايير النجاح: وقت التنفيذ أقل من 30 ثانية
```

### 5.2 اختبار أداء الاستعلامات

#### الاختبار 5.2.1: قياس وقت تنفيذ الاستعلامات
```sql
-- خطوات التنفيذ:
-- 1. قياس وقت تنفيذ استعلام معقد
DECLARE @StartTime DATETIME = GETDATE();

SELECT * FROM vw_RawMaterialsDetails;

DECLARE @EndTime DATETIME = GETDATE();
SELECT DATEDIFF(MILLISECOND, @StartTime, @EndTime) AS [وقت التنفيذ بالمللي ثانية];

-- معايير النجاح: وقت التنفيذ أقل من 1000 مللي ثانية
```

---

## 6. اختبارات حالات الخطأ

### 6.1 اختبار البيانات المكررة

#### الاختبار 6.1.1: إدخال مورد مكرر
```sql
-- خطوات التنفيذ:
-- 1. محاولة إدخال مورد بنفس الاسم
INSERT INTO Suppliers (SupplierName, Phone) 
VALUES ('شركة الخليج للمواد الغذائية', '123456789');

-- النتيجة المتوقعة: قبول الإدراج (لا يوجد قيد تفرد على الاسم)
-- أو رفض الإدراج إذا كان هناك قيد تفرد
```

### 6.2 اختبار حذف السجلات المرتبطة

#### الاختبار 6.2.1: حذف فئة لها مواد خام
```sql
-- خطوات التنفيذ:
-- 1. محاولة حذف فئة مرتبطة بمواد خام
DELETE FROM Categories WHERE CategoryID = 1;

-- النتيجة المتوقعة: خطأ - لا يمكن حذف السجل المرتبط
-- معايير النجاح: منع الحذف مع رسالة خطأ واضحة
```

### 6.3 اختبار تعديل المفاتيح الأساسية

#### الاختبار 6.3.1: تعديل مفتاح أساسي مرتبط
```sql
-- خطوات التنفيذ:
-- 1. محاولة تعديل مفتاح أساسي له مراجع
UPDATE Suppliers SET SupplierID = 999 WHERE SupplierID = 1;

-- النتيجة المتوقعة: خطأ أو تحديث متتالي
-- معايير النجاح: سلوك متسق مع إعدادات العلاقات
```

---

## ملخص معايير النجاح العامة

### 1. معايير الأداء:
- ✅ وقت تنفيذ الاستعلامات < 1 ثانية للبيانات العادية
- ✅ وقت تنفيذ الاستعلامات < 5 ثواني للبيانات الكبيرة
- ✅ استهلاك الذاكرة < 100 ميجابايت

### 2. معايير الدقة:
- ✅ دقة الحسابات 100%
- ✅ سلامة البيانات مضمونة
- ✅ عدم فقدان البيانات

### 3. معايير الموثوقية:
- ✅ عدم كسر النظام تحت أي ظرف
- ✅ رسائل خطأ واضحة ومفيدة
- ✅ استرداد البيانات بعد الأخطاء

### 4. معايير قابلية الاستخدام:
- ✅ سهولة تنفيذ العمليات
- ✅ وضوح النتائج
- ✅ توافق مع متطلبات العمل

---

## تنفيذ الاختبارات

### 1. الاختبارات التلقائية:
```powershell
# تنفيذ جميع الاختبارات
.\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory "All" -Detailed

# تنفيذ اختبارات محددة
.\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory "Tables" -Detailed
.\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory "Queries" -Detailed
.\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory "Performance" -Detailed

# تنفيذ مع التوقف عند أول خطأ
.\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory "All" -StopOnError
```

### 2. الاختبارات اليدوية:
- فتح قاعدة البيانات في Access
- التنقل بين الجداول والاستعلامات
- اختبار النماذج والتقارير (بعد إنشائها)
- اختبار واجهة المستخدم

### 3. اختبارات التكامل:
- اختبار العمليات المتتالية
- اختبار التزامن مع عدة مستخدمين
- اختبار النسخ الاحتياطي والاستعادة

## تقرير الاختبارات

سيتم إنشاء تقرير مفصل يحتوي على:
- نتائج جميع الاختبارات
- أوقات التنفيذ
- تفاصيل الأخطاء
- توصيات للتحسين

## الخطوات التالية

بعد إكمال هذه الاختبارات:
1. **مراجعة تقرير الاختبارات** المُنشأ تلقائياً
2. **إصلاح أي مشاكل** مكتشفة في قاعدة البيانات
3. **إعادة تشغيل الاختبارات** المتأثرة بالإصلاحات
4. **توثيق النتائج النهائية** في تقرير شامل
5. **الموافقة على النظام** للاستخدام الإنتاجي
6. **تدريب المستخدمين** على النظام
7. **وضع خطة الصيانة** والتحديثات المستقبلية

## ملاحظات مهمة

- ⚠️ **تأكد من عمل نسخة احتياطية** قبل تنفيذ الاختبارات
- 🔄 **قم بتنفيذ الاختبارات بانتظام** بعد أي تعديلات
- 📊 **راقب الأداء** وقم بالتحسينات اللازمة
- 🛡️ **اختبر الأمان** وصلاحيات المستخدمين
- 📝 **وثق أي مشاكل** وحلولها للمرجعية المستقبلية
