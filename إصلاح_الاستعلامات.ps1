# سكريبت إصلاح الاستعلامات المعطلة في نظام إدارة المصنع

param(
    [string]$DatabasePath = "نظام_إدارة_المصنع.accdb"
)

Write-Host "=== بدء إصلاح الاستعلامات المعطلة ===" -ForegroundColor Green

try {
    # إنشاء اتصال بقاعدة البيانات
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$DatabasePath")
    
    Write-Host "تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # حذف الاستعلامات المعطلة أولاً
    $brokenViews = @(
        "vw_RawMaterialsDetails",
        "vw_RecipeTotalCost", 
        "vw_PurchaseOrdersDetails",
        "qry_LowStockMaterials",
        "qry_MonthlyPurchases",
        "qry_ProductionOrdersDetails"
    )
    
    Write-Host "حذف الاستعلامات المعطلة..." -ForegroundColor Yellow
    foreach ($viewName in $brokenViews) {
        try {
            $connection.Execute("DROP VIEW [$viewName]")
            Write-Host "✅ تم حذف $viewName" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ لم يتم العثور على $viewName أو تم حذفه مسبقاً" -ForegroundColor Yellow
        }
    }
    
    # إعادة إنشاء الاستعلامات بأسماء الحقول الصحيحة
    Write-Host "`nإعادة إنشاء الاستعلامات المصححة..." -ForegroundColor Yellow
    
    # 1. استعلام المواد الخام المصحح
    $sql1 = @"
CREATE VIEW vw_RawMaterialsDetails AS
SELECT 
    rm.MaterialID,
    rm.MaterialCode,
    rm.MaterialName,
    c.CategoryName,
    u.UnitName,
    rm.CurrentStock,
    rm.MinStockLevel,
    rm.MaxStockLevel,
    rm.UnitCost,
    IIf(rm.CurrentStock <= rm.MinStockLevel, 'منخفض', 
        IIf(rm.CurrentStock >= rm.MaxStockLevel, 'مرتفع', 'طبيعي')) AS StockStatus,
    rm.CurrentStock * rm.UnitCost AS TotalValue,
    s.SupplierName
FROM RawMaterials rm
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID
LEFT JOIN Units u ON rm.UnitID = u.UnitID
LEFT JOIN Suppliers s ON rm.SupplierID = s.SupplierID
WHERE rm.IsActive = True
"@
    
    try {
        $connection.Execute($sql1)
        Write-Host "✅ تم إنشاء vw_RawMaterialsDetails" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_RawMaterialsDetails: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 2. استعلام تكلفة الوصفات المصحح
    $sql2 = @"
CREATE VIEW vw_RecipeTotalCost AS
SELECT 
    r.RecipeID,
    r.RecipeName,
    p.ProductName,
    SUM(ri.Quantity * rm.UnitCost) AS [إجمالي التكلفة],
    r.ProducedQuantity AS [الكمية المنتجة],
    IIf(r.ProducedQuantity > 0, SUM(ri.Quantity * rm.UnitCost) / r.ProducedQuantity, 0) AS [تكلفة الوحدة]
FROM Recipes r
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
LEFT JOIN Products p ON r.ProductID = p.ProductID
WHERE r.IsActive = True
GROUP BY r.RecipeID, r.RecipeName, p.ProductName, r.ProducedQuantity
"@
    
    try {
        $connection.Execute($sql2)
        Write-Host "✅ تم إنشاء vw_RecipeTotalCost" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_RecipeTotalCost: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 3. استعلام تفاصيل طلبات الشراء المصحح
    $sql3 = @"
CREATE VIEW vw_PurchaseOrdersDetails AS
SELECT 
    po.OrderID,
    po.OrderNumber AS [رقم الطلب],
    s.SupplierName AS [المورد],
    po.OrderDate AS [تاريخ الطلب],
    po.Status AS [الحالة],
    pod.MaterialID,
    rm.MaterialName AS [المادة الخام],
    pod.Quantity AS [الكمية],
    u.UnitName AS [الوحدة],
    pod.UnitPrice AS [سعر الوحدة],
    pod.Quantity * pod.UnitPrice AS [إجمالي السطر]
FROM PurchaseOrders po
INNER JOIN PurchaseOrderDetails pod ON po.OrderID = pod.OrderID
INNER JOIN RawMaterials rm ON pod.MaterialID = rm.MaterialID
INNER JOIN Suppliers s ON po.SupplierID = s.SupplierID
LEFT JOIN Units u ON rm.UnitID = u.UnitID
"@
    
    try {
        $connection.Execute($sql3)
        Write-Host "✅ تم إنشاء vw_PurchaseOrdersDetails" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_PurchaseOrdersDetails: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 4. استعلام المواد منخفضة المخزون المصحح
    $sql4 = @"
CREATE VIEW qry_LowStockMaterials AS
SELECT 
    rm.MaterialID,
    rm.MaterialName AS [المادة الخام],
    rm.CurrentStock AS [المخزون الحالي],
    rm.MinStockLevel AS [الحد الأدنى],
    rm.MinStockLevel - rm.CurrentStock AS [الكمية المطلوبة],
    s.SupplierName AS [المورد المفضل],
    rm.UnitCost AS [تكلفة الوحدة],
    (rm.MinStockLevel - rm.CurrentStock) * rm.UnitCost AS [تكلفة إعادة الطلب]
FROM RawMaterials rm
LEFT JOIN Suppliers s ON rm.SupplierID = s.SupplierID
WHERE rm.CurrentStock <= rm.MinStockLevel 
AND rm.IsActive = True
ORDER BY (rm.MinStockLevel - rm.CurrentStock) DESC
"@
    
    try {
        $connection.Execute($sql4)
        Write-Host "✅ تم إنشاء qry_LowStockMaterials" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_LowStockMaterials: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 5. استعلام المشتريات الشهرية المصحح
    $sql5 = @"
CREATE VIEW qry_MonthlyPurchases AS
SELECT 
    Format([InvoiceDate], 'yyyy-mm') AS [الشهر],
    COUNT(*) AS [عدد الفواتير],
    SUM(pi.SubTotal) AS [إجمالي المشتريات],
    SUM(pi.TaxAmount) AS [إجمالي الضرائب],
    SUM(pi.NetAmount) AS [صافي المبلغ],
    AVG(pi.NetAmount) AS [متوسط الفاتورة]
FROM PurchaseInvoices pi
WHERE pi.InvoiceDate >= DateAdd('m', -12, Date())
GROUP BY Format([InvoiceDate], 'yyyy-mm')
ORDER BY Format([InvoiceDate], 'yyyy-mm') DESC
"@
    
    try {
        $connection.Execute($sql5)
        Write-Host "✅ تم إنشاء qry_MonthlyPurchases" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_MonthlyPurchases: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 6. استعلام تفاصيل أوامر الإنتاج المصحح
    $sql6 = @"
CREATE VIEW qry_ProductionOrdersDetails AS
SELECT 
    po.ProductionOrderID,
    po.OrderNumber AS [رقم أمر الإنتاج],
    p.ProductName AS [المنتج],
    r.RecipeName AS [الوصفة],
    po.PlannedQuantity AS [الكمية المخططة],
    po.ActualQuantity AS [الكمية الفعلية],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    po.Status AS [الحالة],
    IIf(po.ActualQuantity > 0, po.ActualQuantity / po.PlannedQuantity * 100, 0) AS [نسبة الإنجاز]
FROM ProductionOrders po
LEFT JOIN Products p ON po.ProductID = p.ProductID
LEFT JOIN Recipes r ON po.RecipeID = r.RecipeID
"@
    
    try {
        $connection.Execute($sql6)
        Write-Host "✅ تم إنشاء qry_ProductionOrdersDetails" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_ProductionOrdersDetails: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # إضافة استعلام جديد لقيمة المخزون الإجمالية
    $sql7 = @"
CREATE VIEW qry_InventoryValue AS
SELECT 
    'المواد الخام' AS [نوع المخزون],
    COUNT(*) AS [عدد الأصناف],
    SUM(rm.CurrentStock) AS [إجمالي الكمية],
    SUM(rm.CurrentStock * rm.UnitCost) AS [إجمالي القيمة]
FROM RawMaterials rm
WHERE rm.IsActive = True
UNION ALL
SELECT 
    'المنتجات النهائية' AS [نوع المخزون],
    COUNT(*) AS [عدد الأصناف],
    SUM(p.CurrentStock) AS [إجمالي الكمية],
    SUM(p.CurrentStock * p.ProductionCost) AS [إجمالي القيمة]
FROM Products p
WHERE p.IsActive = True
"@
    
    try {
        $connection.Execute($sql7)
        Write-Host "✅ تم إنشاء qry_InventoryValue" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_InventoryValue: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n=== تم إكمال إصلاح الاستعلامات بنجاح ===" -ForegroundColor Green
    Write-Host "يمكنك الآن اختبار الاستعلامات في Access" -ForegroundColor Cyan
    
} catch {
    Write-Error "حدث خطأ عام: $($_.Exception.Message)"
} finally {
    # إغلاق الاتصال
    if ($connection) {
        try {
            $connection.Close()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
        } catch {
            # تجاهل أخطاء الإغلاق
        }
    }
}

Write-Host "`nلاختبار الإصلاحات، قم بتشغيل:" -ForegroundColor Yellow
Write-Host ".\تنفيذ_الاختبارات_المتقدمة.ps1 -TestCategory 'Queries' -Detailed" -ForegroundColor Cyan
