-- استعلامات مفيدة لنظام إدارة المصنع

-- 1. استعلام المواد الخام مع معلومات الفئة والوحدة
CREATE VIEW vw_RawMaterialsDetails AS
SELECT 
    rm.MaterialID,
    rm.MaterialCode,
    rm.MaterialName,
    c.CategoryName,
    u.UnitNameArabic,
    rm.CurrentStock,
    rm.MinStockLevel,
    rm.MaxStockLevel,
    rm.UnitCost,
    IIf(rm.CurrentStock <= rm.MinStockLevel, "منخفض", 
        IIf(rm.CurrentStock >= rm.MaxStockLevel, "مرتفع", "طبيعي")) AS StockStatus,
    rm.CurrentStock * rm.UnitCost AS TotalValue
FROM RawMaterials rm
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID
LEFT JOIN Units u ON rm.UnitID = u.UnitID
WHERE rm.IsActive = True;

-- 2. استعلام المنتجات مع معلومات الفئة والوحدة
CREATE VIEW vw_ProductsDetails AS
SELECT 
    p.ProductID,
    p.ProductCode,
    p.ProductName,
    c.CategoryName,
    u.UnitNameArabic,
    p.CurrentStock,
    p.MinStockLevel,
    p.MaxStockLevel,
    p.SalePrice,
    p.ProductionCost,
    p.SalePrice - p.ProductionCost AS ProfitMargin,
    IIf(p.CurrentStock <= p.MinStockLevel, "منخفض", 
        IIf(p.CurrentStock >= p.MaxStockLevel, "مرتفع", "طبيعي")) AS StockStatus,
    p.CurrentStock * p.SalePrice AS TotalValue
FROM Products p
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID
LEFT JOIN Units u ON p.UnitID = u.UnitID
WHERE p.IsActive = True;

-- 3. استعلام تفاصيل الوصفات مع التكلفة
CREATE VIEW vw_RecipeDetails AS
SELECT 
    r.RecipeID,
    r.RecipeName,
    p.ProductName,
    r.BatchSize,
    ri.MaterialID,
    rm.MaterialName,
    ri.Quantity,
    u.UnitNameArabic,
    rm.UnitCost,
    ri.Quantity * rm.UnitCost AS IngredientCost
FROM Recipes r
INNER JOIN Products p ON r.ProductID = p.ProductID
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
INNER JOIN Units u ON ri.UnitID = u.UnitID
WHERE r.IsActive = True;

-- 4. استعلام تكلفة الوصفات الإجمالية
CREATE VIEW vw_RecipeTotalCost AS
SELECT 
    r.RecipeID,
    r.RecipeName,
    p.ProductName,
    r.BatchSize,
    Sum(ri.Quantity * rm.UnitCost) AS TotalCost,
    Sum(ri.Quantity * rm.UnitCost) / r.BatchSize AS CostPerUnit
FROM Recipes r
INNER JOIN Products p ON r.ProductID = p.ProductID
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
WHERE r.IsActive = True
GROUP BY r.RecipeID, r.RecipeName, p.ProductName, r.BatchSize;

-- 5. استعلام طلبات الشراء مع تفاصيل المورد
CREATE VIEW vw_PurchaseOrdersDetails AS
SELECT 
    po.PurchaseOrderID,
    po.OrderNumber,
    s.SupplierName,
    s.ContactPerson,
    s.Phone,
    po.OrderDate,
    po.RequiredDate,
    po.Status,
    po.NetAmount,
    DateDiff("d", po.OrderDate, Now()) AS DaysFromOrder
FROM PurchaseOrders po
INNER JOIN Suppliers s ON po.SupplierID = s.SupplierID;

-- 6. استعلام فواتير الشراء مع حالة الدفع
CREATE VIEW vw_PurchaseInvoicesDetails AS
SELECT 
    pi.InvoiceID,
    pi.InvoiceNumber,
    s.SupplierName,
    pi.InvoiceDate,
    pi.DueDate,
    pi.NetAmount,
    pi.PaidAmount,
    pi.NetAmount - pi.PaidAmount AS RemainingAmount,
    pi.Status,
    IIf(pi.DueDate < Now() AND pi.Status <> "Paid", "متأخر", "في الموعد") AS PaymentStatus
FROM PurchaseInvoices pi
INNER JOIN Suppliers s ON pi.SupplierID = s.SupplierID;

-- 7. استعلام أوامر الإنتاج مع التفاصيل
CREATE VIEW vw_ProductionOrdersDetails AS
SELECT 
    po.ProductionOrderID,
    po.OrderNumber,
    p.ProductName,
    r.RecipeName,
    po.PlannedQuantity,
    po.ProducedQuantity,
    po.PlannedStartDate,
    po.PlannedEndDate,
    po.Status,
    po.Priority,
    IIf(po.Priority = 1, "عادي", IIf(po.Priority = 2, "مهم", "عاجل")) AS PriorityText,
    po.PlannedQuantity - po.ProducedQuantity AS RemainingQuantity
FROM ProductionOrders po
INNER JOIN Products p ON po.ProductID = p.ProductID
INNER JOIN Recipes r ON po.RecipeID = r.RecipeID;

-- 8. استعلام فواتير المبيعات مع تفاصيل العميل
CREATE VIEW vw_SalesInvoicesDetails AS
SELECT 
    si.InvoiceID,
    si.InvoiceNumber,
    c.CustomerName,
    c.ContactPerson,
    c.Phone,
    si.InvoiceDate,
    si.DueDate,
    si.NetAmount,
    si.PaidAmount,
    si.NetAmount - si.PaidAmount AS RemainingAmount,
    si.Status,
    IIf(si.DueDate < Now() AND si.Status <> "Paid", "متأخر", "في الموعد") AS PaymentStatus
FROM SalesInvoices si
INNER JOIN Customers c ON si.CustomerID = c.CustomerID;

-- 9. استعلام حركات المخزون التفصيلية
CREATE VIEW vw_InventoryMovementsDetails AS
SELECT 
    im.MovementID,
    im.MovementDate,
    im.MovementType,
    im.ItemType,
    IIf(im.ItemType = "RawMaterial", 
        (SELECT MaterialName FROM RawMaterials WHERE MaterialID = im.ItemID),
        (SELECT ProductName FROM Products WHERE ProductID = im.ItemID)) AS ItemName,
    im.Quantity,
    im.UnitCost,
    im.TotalCost,
    im.ReferenceType,
    im.Notes
FROM InventoryMovements im;

-- 10. استعلام المواد الخام التي تحتاج إعادة طلب
SELECT 
    rm.MaterialCode,
    rm.MaterialName,
    rm.CurrentStock,
    rm.MinStockLevel,
    rm.MinStockLevel - rm.CurrentStock AS QuantityNeeded,
    s.SupplierName,
    s.Phone
FROM RawMaterials rm
LEFT JOIN (
    SELECT MaterialID, SupplierID
    FROM PurchaseInvoiceDetails pid
    INNER JOIN PurchaseInvoices pi ON pid.InvoiceID = pi.InvoiceID
    WHERE pi.InvoiceDate = (
        SELECT MAX(pi2.InvoiceDate)
        FROM PurchaseInvoiceDetails pid2
        INNER JOIN PurchaseInvoices pi2 ON pid2.InvoiceID = pi2.InvoiceID
        WHERE pid2.MaterialID = pid.MaterialID
    )
) AS LastPurchase ON rm.MaterialID = LastPurchase.MaterialID
LEFT JOIN Suppliers s ON LastPurchase.SupplierID = s.SupplierID
WHERE rm.CurrentStock <= rm.MinStockLevel AND rm.IsActive = True;

-- 11. استعلام أفضل العملاء (حسب المبيعات)
SELECT 
    c.CustomerName,
    c.ContactPerson,
    c.Phone,
    Sum(si.NetAmount) AS TotalSales,
    Count(si.InvoiceID) AS InvoiceCount,
    Avg(si.NetAmount) AS AverageSale,
    Max(si.InvoiceDate) AS LastSaleDate
FROM Customers c
INNER JOIN SalesInvoices si ON c.CustomerID = si.CustomerID
GROUP BY c.CustomerID, c.CustomerName, c.ContactPerson, c.Phone
ORDER BY Sum(si.NetAmount) DESC;

-- 12. استعلام أفضل المنتجات مبيعاً
SELECT 
    p.ProductName,
    Sum(sid.Quantity) AS TotalQuantitySold,
    Sum(sid.NetPrice) AS TotalSales,
    Avg(sid.UnitPrice) AS AveragePrice,
    Count(DISTINCT si.CustomerID) AS UniqueCustomers
FROM Products p
INNER JOIN SalesInvoiceDetails sid ON p.ProductID = sid.ProductID
INNER JOIN SalesInvoices si ON sid.InvoiceID = si.InvoiceID
GROUP BY p.ProductID, p.ProductName
ORDER BY Sum(sid.NetPrice) DESC;

-- 13. استعلام تقرير الربحية الشهرية
SELECT 
    Format(si.InvoiceDate, "yyyy-mm") AS SalesMonth,
    Sum(sid.NetPrice) AS TotalSales,
    Sum(sid.Quantity * p.ProductionCost) AS TotalCost,
    Sum(sid.NetPrice) - Sum(sid.Quantity * p.ProductionCost) AS GrossProfit,
    (Sum(sid.NetPrice) - Sum(sid.Quantity * p.ProductionCost)) / Sum(sid.NetPrice) * 100 AS ProfitMarginPercent
FROM SalesInvoices si
INNER JOIN SalesInvoiceDetails sid ON si.InvoiceID = sid.InvoiceID
INNER JOIN Products p ON sid.ProductID = p.ProductID
GROUP BY Format(si.InvoiceDate, "yyyy-mm")
ORDER BY Format(si.InvoiceDate, "yyyy-mm") DESC;

-- 14. استعلام حالة المخزون الحالية
SELECT 
    "مواد خام" AS ItemType,
    Count(*) AS TotalItems,
    Sum(IIf(CurrentStock <= MinStockLevel, 1, 0)) AS LowStockItems,
    Sum(CurrentStock * UnitCost) AS TotalValue
FROM RawMaterials
WHERE IsActive = True
UNION ALL
SELECT 
    "منتجات نهائية" AS ItemType,
    Count(*) AS TotalItems,
    Sum(IIf(CurrentStock <= MinStockLevel, 1, 0)) AS LowStockItems,
    Sum(CurrentStock * SalePrice) AS TotalValue
FROM Products
WHERE IsActive = True;
