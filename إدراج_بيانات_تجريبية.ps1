# سكريبت PowerShell لإدراج بيانات تجريبية في قاعدة البيانات

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

try {
    # إنشاء اتصال بقاعدة البيانات
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath")
    
    Write-Host "تم الاتصال بقاعدة البيانات" -ForegroundColor Green
    
    # إدراج موردين تجريبيين
    Write-Host "إدراج الموردين..."
    $suppliers = @(
        @("شركة الدقيق الذهبي", "أحمد محمد", "011-1234567", "**********", "<EMAIL>", "شارع الملك فهد، الرياض", "الرياض", "السعودية", "123456789", 5),
        @("مؤسسة المكسرات الطبيعية", "فاطمة علي", "011-2345678", "**********", "<EMAIL>", "حي النخيل، جدة", "جدة", "السعودية", "234567890", 4),
        @("شركة العسل الجبلي", "محمد سالم", "011-3456789", "**********", "<EMAIL>", "طريق الملك عبدالعزيز، الدمام", "الدمام", "السعودية", "*********", 5),
        @("مصنع الزيوت النقية", "سارة أحمد", "011-4567890", "**********", "<EMAIL>", "المنطقة الصناعية، الرياض", "الرياض", "السعودية", "*********", 4)
    )
    
    foreach ($supplier in $suppliers) {
        $sql = "INSERT INTO Suppliers (SupplierName, ContactPerson, Phone, Mobile, Email, Address, City, Country, TaxNumber, Rating) VALUES ('$($supplier[0])', '$($supplier[1])', '$($supplier[2])', '$($supplier[3])', '$($supplier[4])', '$($supplier[5])', '$($supplier[6])', '$($supplier[7])', '$($supplier[8])', $($supplier[9]))"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج الموردين بنجاح"
    
    # إدراج مواد خام تجريبية
    Write-Host "إدراج المواد الخام..."
    $rawMaterials = @(
        @("RM001", "دقيق أبيض فاخر", 1, 1, 50, 500, 200, 3.50),
        @("RM002", "جوز مقشر", 2, 1, 10, 100, 25, 45.00),
        @("RM003", "عسل طبيعي", 3, 3, 5, 50, 15, 85.00),
        @("RM004", "زيت زيتون", 4, 3, 3, 30, 8, 25.00),
        @("RM005", "سكر ناعم", 3, 1, 20, 200, 75, 2.80),
        @("RM006", "فانيليا", 5, 4, 1, 10, 3, 120.00),
        @("RM007", "قرفة مطحونة", 5, 1, 2, 20, 5, 35.00),
        @("RM008", "أكياس تعبئة صغيرة", 6, 5, 100, 1000, 300, 0.15),
        @("RM009", "صناديق كرتون", 6, 5, 50, 500, 150, 2.50),
        @("RM010", "ملصقات المنتج", 6, 5, 200, 2000, 500, 0.25)
    )
    
    foreach ($material in $rawMaterials) {
        $sql = "INSERT INTO RawMaterials (MaterialCode, MaterialName, CategoryID, UnitID, MinStockLevel, MaxStockLevel, CurrentStock, UnitCost) VALUES ('$($material[0])', '$($material[1])', $($material[2]), $($material[3]), $($material[4]), $($material[5]), $($material[6]), $($material[7]))"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج المواد الخام بنجاح"
    
    # إدراج منتجات تجريبية
    Write-Host "إدراج المنتجات..."
    $products = @(
        @("PRD001", "معمول بالجوز - حجم صغير", 7, 5, 20, 200, 50, 15.00, 8.50),
        @("PRD002", "معمول بالجوز - حجم كبير", 7, 5, 10, 100, 25, 25.00, 14.00),
        @("PRD003", "معمول بالعسل - حجم صغير", 8, 5, 20, 200, 40, 18.00, 10.00),
        @("PRD004", "معمول بالعسل - حجم كبير", 8, 5, 10, 100, 20, 30.00, 16.50),
        @("PRD005", "معمول مشكل", 9, 6, 5, 50, 15, 45.00, 25.00)
    )
    
    foreach ($product in $products) {
        $sql = "INSERT INTO Products (ProductCode, ProductName, CategoryID, UnitID, MinStockLevel, MaxStockLevel, CurrentStock, SalePrice, ProductionCost) VALUES ('$($product[0])', '$($product[1])', $($product[2]), $($product[3]), $($product[4]), $($product[5]), $($product[6]), $($product[7]), $($product[8]))"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج المنتجات بنجاح"
    
    # إدراج وصفات تجريبية
    Write-Host "إدراج الوصفات..."
    
    # وصفة معمول بالجوز - حجم صغير
    $sql = "INSERT INTO Recipes (ProductID, RecipeName, BatchSize, PreparationTime, CookingTime, TotalTime, Instructions) VALUES (1, 'وصفة معمول الجوز الصغير', 50, 120, 180, 300, 'خلط الدقيق مع الزيت والماء، تحضير حشوة الجوز، تشكيل المعمول، الخبز في فرن متوسط الحرارة')"
    $connection.Execute($sql) | Out-Null
    
    # مكونات وصفة معمول الجوز الصغير
    $recipeIngredients = @(
        @(1, 1, 5.0, 1),    # دقيق
        @(1, 2, 2.0, 1),    # جوز
        @(1, 4, 0.5, 3),    # زيت زيتون
        @(1, 5, 1.0, 1),    # سكر
        @(1, 6, 0.01, 4),   # فانيليا
        @(1, 7, 0.05, 1)    # قرفة
    )
    
    foreach ($ingredient in $recipeIngredients) {
        $sql = "INSERT INTO RecipeIngredients (RecipeID, MaterialID, Quantity, UnitID) VALUES ($($ingredient[0]), $($ingredient[1]), $($ingredient[2]), $($ingredient[3]))"
        $connection.Execute($sql) | Out-Null
    }
    
    # وصفة معمول بالعسل - حجم صغير
    $sql = "INSERT INTO Recipes (ProductID, RecipeName, BatchSize, PreparationTime, CookingTime, TotalTime, Instructions) VALUES (3, 'وصفة معمول العسل الصغير', 50, 100, 160, 260, 'خلط الدقيق مع الزيت والعسل، تحضير حشوة العسل، تشكيل المعمول، الخبز في فرن متوسط الحرارة')"
    $connection.Execute($sql) | Out-Null
    
    # مكونات وصفة معمول العسل الصغير
    $recipeIngredients2 = @(
        @(2, 1, 4.5, 1),    # دقيق
        @(2, 3, 1.5, 3),    # عسل
        @(2, 4, 0.4, 3),    # زيت زيتون
        @(2, 5, 0.5, 1),    # سكر
        @(2, 6, 0.01, 4),   # فانيليا
        @(2, 7, 0.03, 1)    # قرفة
    )
    
    foreach ($ingredient in $recipeIngredients2) {
        $sql = "INSERT INTO RecipeIngredients (RecipeID, MaterialID, Quantity, UnitID) VALUES ($($ingredient[0]), $($ingredient[1]), $($ingredient[2]), $($ingredient[3]))"
        $connection.Execute($sql) | Out-Null
    }
    
    Write-Host "تم إدراج الوصفات بنجاح"
    
    # إدراج عملاء تجريبيين
    Write-Host "إدراج العملاء..."
    $customers = @(
        @("CUST001", "محل الحلويات الشرقية", "خالد أحمد", "011-5555555", "0505555555", "<EMAIL>", "شارع العليا، الرياض", "الرياض", "السعودية", "555555555", 50000, 0),
        @("CUST002", "سوبر ماركت النخيل", "عبدالله محمد", "011-6666666", "0506666666", "<EMAIL>", "حي الملز، الرياض", "الرياض", "السعودية", "666666666", 30000, 0),
        @("CUST003", "مخبز الأصالة", "فهد سالم", "011-7777777", "0507777777", "<EMAIL>", "شارع الأمير سلطان، جدة", "جدة", "السعودية", "*********", 25000, 0),
        @("CUST004", "متجر الحلويات الفاخرة", "نورا علي", "011-8888888", "0508888888", "<EMAIL>", "كورنيش الدمام", "الدمام", "السعودية", "*********", 40000, 0)
    )
    
    foreach ($customer in $customers) {
        $sql = "INSERT INTO Customers (CustomerCode, CustomerName, ContactPerson, Phone, Mobile, Email, Address, City, Country, TaxNumber, CreditLimit, CurrentBalance) VALUES ('$($customer[0])', '$($customer[1])', '$($customer[2])', '$($customer[3])', '$($customer[4])', '$($customer[5])', '$($customer[6])', '$($customer[7])', '$($customer[8])', '$($customer[9])', $($customer[10]), $($customer[11]))"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج العملاء بنجاح"
    
    # إدراج طلب شراء تجريبي
    Write-Host "إدراج طلب شراء تجريبي..."
    $sql = "INSERT INTO PurchaseOrders (OrderNumber, SupplierID, OrderDate, RequiredDate, Status, TotalAmount, TaxAmount, NetAmount, CreatedBy) VALUES ('PO-2024-001', 1, Now(), DateAdd('d', 7, Now()), 'Pending', 1000, 150, 1150, 'مدير المشتريات')"
    $connection.Execute($sql) | Out-Null
    
    # تفاصيل طلب الشراء
    $sql = "INSERT INTO PurchaseOrderDetails (PurchaseOrderID, MaterialID, Quantity, UnitPrice, TotalPrice) VALUES (1, 1, 100, 3.50, 350)"
    $connection.Execute($sql) | Out-Null
    $sql = "INSERT INTO PurchaseOrderDetails (PurchaseOrderID, MaterialID, Quantity, UnitPrice, TotalPrice) VALUES (1, 2, 10, 45.00, 450)"
    $connection.Execute($sql) | Out-Null
    $sql = "INSERT INTO PurchaseOrderDetails (PurchaseOrderID, MaterialID, Quantity, UnitPrice, TotalPrice) VALUES (1, 3, 5, 85.00, 425)"
    $connection.Execute($sql) | Out-Null
    
    Write-Host "تم إدراج طلب الشراء التجريبي"
    
    # إدراج أمر إنتاج تجريبي
    Write-Host "إدراج أمر إنتاج تجريبي..."
    $sql = "INSERT INTO ProductionOrders (OrderNumber, ProductID, RecipeID, PlannedQuantity, PlannedStartDate, PlannedEndDate, Status, Priority, CreatedBy) VALUES ('PROD-2024-001', 1, 1, 100, Now(), DateAdd('d', 2, Now()), 'Planned', 2, 'مدير الإنتاج')"
    $connection.Execute($sql) | Out-Null
    
    Write-Host "تم إدراج أمر الإنتاج التجريبي"
    
    # إدراج فاتورة مبيعات تجريبية
    Write-Host "إدراج فاتورة مبيعات تجريبية..."
    $sql = "INSERT INTO SalesInvoices (InvoiceNumber, CustomerID, InvoiceDate, DueDate, TotalAmount, TaxAmount, NetAmount, Status, SalesmanName) VALUES ('INV-2024-001', 1, Now(), DateAdd('d', 30, Now()), 1500, 225, 1725, 'Unpaid', 'أحمد المبيعات')"
    $connection.Execute($sql) | Out-Null
    
    # تفاصيل فاتورة المبيعات
    $sql = "INSERT INTO SalesInvoiceDetails (InvoiceID, ProductID, Quantity, UnitPrice, TotalPrice, NetPrice) VALUES (1, 1, 50, 15.00, 750, 750)"
    $connection.Execute($sql) | Out-Null
    $sql = "INSERT INTO SalesInvoiceDetails (InvoiceID, ProductID, Quantity, UnitPrice, TotalPrice, NetPrice) VALUES (1, 3, 30, 18.00, 540, 540)"
    $connection.Execute($sql) | Out-Null
    
    Write-Host "تم إدراج فاتورة المبيعات التجريبية"
    
    # إغلاق الاتصال
    $connection.Close()
    
    Write-Host "تم إدراج جميع البيانات التجريبية بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Error "حدث خطأ أثناء إدراج البيانات: $($_.Exception.Message)"
} finally {
    # تنظيف الكائنات
    if ($connection) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
    }
}
