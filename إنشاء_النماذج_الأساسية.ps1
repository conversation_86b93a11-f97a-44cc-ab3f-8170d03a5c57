# سكريبت PowerShell لإنشاء النماذج الأساسية في قاعدة البيانات

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

try {
    Write-Host "=== بدء إنشاء النماذج الأساسية ===" -ForegroundColor Green
    
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true  # جعل Access مرئياً لإنشاء النماذج
    
    # فتح قاعدة البيانات
    $access.OpenCurrentDatabase($dbPath)
    
    Write-Host "تم فتح قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # قائمة النماذج المطلوب إنشاؤها
    $forms = @(
        @{
            Name = "frm_Suppliers"
            Table = "Suppliers"
            Title = "إدارة الموردين"
            Fields = @("SupplierName", "ContactPerson", "Phone", "Email", "Address", "TaxNumber", "Rating", "IsActive")
        },
        @{
            Name = "frm_RawMaterials"
            Table = "RawMaterials"
            Title = "إدارة المواد الخام"
            Fields = @("MaterialName", "CategoryID", "UnitID", "SupplierID", "CurrentStock", "MinStockLevel", "UnitCost", "IsActive")
        },
        @{
            Name = "frm_Products"
            Table = "Products"
            Title = "إدارة المنتجات"
            Fields = @("ProductName", "CategoryID", "UnitID", "CurrentStock", "MinStockLevel", "SalePrice", "IsActive")
        },
        @{
            Name = "frm_Customers"
            Table = "Customers"
            Title = "إدارة العملاء"
            Fields = @("CustomerName", "ContactPerson", "Phone", "Email", "Address", "TaxNumber", "CreditLimit", "IsActive")
        },
        @{
            Name = "frm_Recipes"
            Table = "Recipes"
            Title = "إدارة الوصفات"
            Fields = @("RecipeName", "Description", "YieldQuantity", "PreparationTime", "IsActive")
        }
    )
    
    Write-Host "بدء إنشاء النماذج..." -ForegroundColor Yellow
    
    $successCount = 0
    $errorCount = 0
    
    foreach ($formInfo in $forms) {
        try {
            Write-Host "   إنشاء نموذج: $($formInfo.Name)" -ForegroundColor Cyan
            
            # حذف النموذج إذا كان موجوداً
            try {
                $access.DoCmd.DeleteObject(2, $formInfo.Name) # 2 = acForm
            } catch {
                # تجاهل الخطأ إذا لم يكن النموذج موجوداً
            }
            
            # إنشاء نموذج جديد باستخدام معالج النماذج
            $access.DoCmd.RunCommand(2065) # acCmdNewObjectForm
            
            # انتظار قليل لإنشاء النموذج
            Start-Sleep -Seconds 2
            
            # الحصول على النموذج النشط
            $form = $access.Screen.ActiveForm
            
            if ($form) {
                # تعيين خصائص النموذج
                $form.Caption = $formInfo.Title
                $form.RecordSource = $formInfo.Table
                
                # حفظ النموذج
                $access.DoCmd.Save(2, $formInfo.Name) # 2 = acForm
                
                # إغلاق النموذج
                $access.DoCmd.Close(2, $formInfo.Name) # 2 = acForm
                
                $successCount++
                Write-Host "   ✓ تم إنشاء النموذج: $($formInfo.Name)" -ForegroundColor Green
            } else {
                $errorCount++
                Write-Host "   ✗ فشل في إنشاء النموذج: $($formInfo.Name)" -ForegroundColor Red
            }
            
        } catch {
            $errorCount++
            Write-Host "   ✗ خطأ في إنشاء النموذج $($formInfo.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # إنشاء النموذج الرئيسي
    Write-Host "`nإنشاء النموذج الرئيسي..." -ForegroundColor Yellow
    
    try {
        # حذف النموذج الرئيسي إذا كان موجوداً
        try {
            $access.DoCmd.DeleteObject(2, "frm_MainMenu")
        } catch {
            # تجاهل الخطأ
        }
        
        # إنشاء نموذج فارغ
        $access.DoCmd.RunCommand(2065) # acCmdNewObjectForm
        Start-Sleep -Seconds 2
        
        $mainForm = $access.Screen.ActiveForm
        if ($mainForm) {
            $mainForm.Caption = "نظام إدارة مصنع المواد الغذائية"
            $mainForm.NavigationButtons = $false
            $mainForm.RecordSelectors = $false
            $mainForm.DividingLines = $false
            
            # حفظ النموذج الرئيسي
            $access.DoCmd.Save(2, "frm_MainMenu")
            $access.DoCmd.Close(2, "frm_MainMenu")
            
            $successCount++
            Write-Host "   ✓ تم إنشاء النموذج الرئيسي: frm_MainMenu" -ForegroundColor Green
        }
        
    } catch {
        $errorCount++
        Write-Host "   ✗ خطأ في إنشاء النموذج الرئيسي: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`nملخص إنشاء النماذج:" -ForegroundColor Cyan
    Write-Host "   النماذج المنشأة بنجاح: $successCount" -ForegroundColor Green
    Write-Host "   الأخطاء: $errorCount" -ForegroundColor $(if($errorCount -eq 0) {"Green"} else {"Red"})
    
    # حفظ قاعدة البيانات
    $access.DoCmd.Save()
    
    Write-Host "`n=== تم إكمال إنشاء النماذج ===" -ForegroundColor Green
    
    if ($errorCount -eq 0) {
        Write-Host "جميع النماذج تم إنشاؤها بنجاح!" -ForegroundColor Green
        Write-Host "يمكنك الآن رؤية النماذج في Access في قسم Forms" -ForegroundColor Yellow
    } else {
        Write-Host "تم إنشاء معظم النماذج، راجع الأخطاء أعلاه" -ForegroundColor Yellow
    }
    
    Write-Host "`nالنماذج المتاحة:" -ForegroundColor Cyan
    Write-Host "   - frm_Suppliers (إدارة الموردين)" -ForegroundColor White
    Write-Host "   - frm_RawMaterials (إدارة المواد الخام)" -ForegroundColor White
    Write-Host "   - frm_Products (إدارة المنتجات)" -ForegroundColor White
    Write-Host "   - frm_Customers (إدارة العملاء)" -ForegroundColor White
    Write-Host "   - frm_Recipes (إدارة الوصفات)" -ForegroundColor White
    Write-Host "   - frm_MainMenu (النموذج الرئيسي)" -ForegroundColor White
    
} catch {
    Write-Error "حدث خطأ عام: $($_.Exception.Message)"
} finally {
    # ملاحظة: لا نغلق Access هنا لأن المستخدم قد يريد رؤية النماذج
    Write-Host "`nملاحظة: تم ترك Access مفتوحاً لتتمكن من رؤية النماذج المنشأة" -ForegroundColor Yellow
}
