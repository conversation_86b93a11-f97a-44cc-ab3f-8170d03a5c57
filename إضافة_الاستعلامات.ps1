# سكريبت PowerShell لإضافة الاستعلامات المتقدمة إلى قاعدة البيانات

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

try {
    Write-Host "=== بدء إضافة الاستعلامات المتقدمة ===" -ForegroundColor Green
    
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # فتح قاعدة البيانات
    $access.OpenCurrentDatabase($dbPath)
    
    Write-Host "تم فتح قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # تعريف الاستعلامات
    $queries = @{
        "vw_RawMaterialsDetails" = @"
SELECT 
    rm.MaterialID,
    rm.MaterialName AS [اسم المادة],
    c.Category<PERSON>ame <PERSON> [الفئة],
    u.UnitName AS [الوحدة],
    s.Supp<PERSON> AS [المورد],
    rm.CurrentStock AS [المخزون الحالي],
    rm.MinStockLevel AS [الحد الأدنى],
    rm.UnitCost AS [تكلفة الوحدة],
    (rm.CurrentStock * rm.UnitCost) AS [قيمة المخزون],
    IIf(rm.CurrentStock <= rm.MinStockLevel, 'نعم', 'لا') AS [يحتاج إعادة طلب]
FROM ((RawMaterials rm 
LEFT JOIN Categories c ON rm.CategoryID = c.CategoryID)
LEFT JOIN Units u ON rm.UnitID = u.UnitID)
LEFT JOIN Suppliers s ON rm.SupplierID = s.SupplierID
WHERE rm.IsActive = True
"@

        "vw_ProductsDetails" = @"
SELECT 
    p.ProductID,
    p.ProductName AS [اسم المنتج],
    c.CategoryName AS [الفئة],
    u.UnitName AS [الوحدة],
    p.CurrentStock AS [المخزون الحالي],
    p.MinStockLevel AS [الحد الأدنى],
    p.SalePrice AS [سعر البيع],
    (p.CurrentStock * p.SalePrice) AS [قيمة المخزون],
    IIf(p.CurrentStock <= p.MinStockLevel, 'نعم', 'لا') AS [يحتاج إنتاج]
FROM (Products p 
LEFT JOIN Categories c ON p.CategoryID = c.CategoryID)
LEFT JOIN Units u ON p.UnitID = u.UnitID
WHERE p.IsActive = True
"@

        "vw_RecipeDetails" = @"
SELECT 
    r.RecipeID,
    r.RecipeName AS [اسم الوصفة],
    rm.MaterialName AS [المادة الخام],
    ri.Quantity AS [الكمية المطلوبة],
    u.UnitName AS [الوحدة],
    rm.UnitCost AS [تكلفة الوحدة],
    (ri.Quantity * rm.UnitCost) AS [تكلفة المادة]
FROM ((Recipes r 
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID)
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID)
LEFT JOIN Units u ON rm.UnitID = u.UnitID
WHERE r.IsActive = True
ORDER BY r.RecipeID, rm.MaterialName
"@

        "vw_RecipeTotalCost" = @"
SELECT 
    r.RecipeID,
    r.RecipeName AS [اسم الوصفة],
    Sum(ri.Quantity * rm.UnitCost) AS [إجمالي التكلفة],
    r.YieldQuantity AS [الكمية المنتجة],
    (Sum(ri.Quantity * rm.UnitCost) / r.YieldQuantity) AS [تكلفة الوحدة]
FROM (Recipes r 
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID)
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
WHERE r.IsActive = True
GROUP BY r.RecipeID, r.RecipeName, r.YieldQuantity
"@

        "vw_PurchaseOrdersDetails" = @"
SELECT 
    po.OrderID,
    po.OrderNumber AS [رقم الطلب],
    s.SupplierName AS [المورد],
    po.OrderDate AS [تاريخ الطلب],
    po.Status AS [الحالة],
    rm.MaterialName AS [المادة],
    pod.Quantity AS [الكمية],
    pod.UnitPrice AS [سعر الوحدة],
    (pod.Quantity * pod.UnitPrice) AS [المجموع]
FROM ((PurchaseOrders po 
INNER JOIN Suppliers s ON po.SupplierID = s.SupplierID)
INNER JOIN PurchaseOrderDetails pod ON po.OrderID = pod.OrderID)
INNER JOIN RawMaterials rm ON pod.MaterialID = rm.MaterialID
ORDER BY po.OrderDate DESC
"@

        "vw_SalesInvoicesDetails" = @"
SELECT 
    si.InvoiceID,
    si.InvoiceNumber AS [رقم الفاتورة],
    c.CustomerName AS [العميل],
    si.InvoiceDate AS [تاريخ الفاتورة],
    p.ProductName AS [المنتج],
    sid.Quantity AS [الكمية],
    sid.UnitPrice AS [سعر الوحدة],
    (sid.Quantity * sid.UnitPrice) AS [المجموع الفرعي],
    si.TaxAmount AS [الضريبة],
    si.NetAmount AS [المجموع النهائي]
FROM ((SalesInvoices si 
INNER JOIN Customers c ON si.CustomerID = c.CustomerID)
INNER JOIN SalesInvoiceDetails sid ON si.InvoiceID = sid.InvoiceID)
INNER JOIN Products p ON sid.ProductID = p.ProductID
ORDER BY si.InvoiceDate DESC
"@

        "vw_InventoryMovementsDetails" = @"
SELECT 
    im.MovementID,
    im.MovementDate AS [تاريخ الحركة],
    im.MovementType AS [نوع الحركة],
    im.ReferenceType AS [نوع المرجع],
    im.ReferenceID AS [رقم المرجع],
    SWITCH(
        im.MaterialID IS NOT NULL, (SELECT MaterialName FROM RawMaterials WHERE MaterialID = im.MaterialID),
        im.ProductID IS NOT NULL, (SELECT ProductName FROM Products WHERE ProductID = im.ProductID)
    ) AS [الصنف],
    im.Quantity AS [الكمية],
    im.Notes AS [ملاحظات]
FROM InventoryMovements im
ORDER BY im.MovementDate DESC
"@

        "qry_LowStockMaterials" = @"
SELECT 
    rm.MaterialID,
    rm.MaterialName AS [المادة الخام],
    rm.CurrentStock AS [المخزون الحالي],
    rm.MinStockLevel AS [الحد الأدنى],
    (rm.MinStockLevel - rm.CurrentStock) AS [الكمية المطلوبة],
    s.SupplierName AS [المورد المفضل]
FROM RawMaterials rm 
LEFT JOIN Suppliers s ON rm.SupplierID = s.SupplierID
WHERE rm.CurrentStock <= rm.MinStockLevel AND rm.IsActive = True
ORDER BY (rm.MinStockLevel - rm.CurrentStock) DESC
"@

        "qry_LowStockProducts" = @"
SELECT 
    p.ProductID,
    p.ProductName AS [المنتج],
    p.CurrentStock AS [المخزون الحالي],
    p.MinStockLevel AS [الحد الأدنى],
    (p.MinStockLevel - p.CurrentStock) AS [الكمية المطلوبة للإنتاج]
FROM Products p
WHERE p.CurrentStock <= p.MinStockLevel AND p.IsActive = True
ORDER BY (p.MinStockLevel - p.CurrentStock) DESC
"@

        "qry_MonthlyPurchases" = @"
SELECT 
    Format(po.OrderDate, 'yyyy-mm') AS [الشهر],
    Count(po.OrderID) AS [عدد الطلبات],
    Sum(pod.Quantity * pod.UnitPrice) AS [إجمالي المشتريات]
FROM PurchaseOrders po 
INNER JOIN PurchaseOrderDetails pod ON po.OrderID = pod.OrderID
WHERE po.OrderDate >= DateAdd('m', -12, Date())
GROUP BY Format(po.OrderDate, 'yyyy-mm')
ORDER BY Format(po.OrderDate, 'yyyy-mm') DESC
"@

        "qry_MonthlySales" = @"
SELECT 
    Format(si.InvoiceDate, 'yyyy-mm') AS [الشهر],
    Count(si.InvoiceID) AS [عدد الفواتير],
    Sum(si.NetAmount) AS [إجمالي المبيعات],
    Sum(si.TaxAmount) AS [إجمالي الضرائب]
FROM SalesInvoices si
WHERE si.InvoiceDate >= DateAdd('m', -12, Date())
GROUP BY Format(si.InvoiceDate, 'yyyy-mm')
ORDER BY Format(si.InvoiceDate, 'yyyy-mm') DESC
"@

        "qry_TopCustomers" = @"
SELECT 
    c.CustomerID,
    c.CustomerName AS [العميل],
    Count(si.InvoiceID) AS [عدد الفواتير],
    Sum(si.NetAmount) AS [إجمالي المبيعات],
    Avg(si.NetAmount) AS [متوسط الفاتورة]
FROM Customers c 
INNER JOIN SalesInvoices si ON c.CustomerID = si.CustomerID
WHERE si.InvoiceDate >= DateAdd('m', -6, Date())
GROUP BY c.CustomerID, c.CustomerName
ORDER BY Sum(si.NetAmount) DESC
"@

        "qry_TopProducts" = @"
SELECT 
    p.ProductID,
    p.ProductName AS [المنتج],
    Sum(sid.Quantity) AS [إجمالي الكمية المباعة],
    Sum(sid.Quantity * sid.UnitPrice) AS [إجمالي المبيعات],
    Count(sid.InvoiceID) AS [عدد مرات البيع]
FROM (Products p 
INNER JOIN SalesInvoiceDetails sid ON p.ProductID = sid.ProductID)
INNER JOIN SalesInvoices si ON sid.InvoiceID = si.InvoiceID
WHERE si.InvoiceDate >= DateAdd('m', -6, Date())
GROUP BY p.ProductID, p.ProductName
ORDER BY Sum(sid.Quantity * sid.UnitPrice) DESC
"@

        "qry_ProductionOrdersDetails" = @"
SELECT 
    po.ProductionOrderID,
    po.OrderNumber AS [رقم أمر الإنتاج],
    p.ProductName AS [المنتج],
    r.RecipeName AS [الوصفة],
    po.PlannedQuantity AS [الكمية المخططة],
    po.ActualQuantity AS [الكمية الفعلية],
    po.StartDate AS [تاريخ البدء],
    po.EndDate AS [تاريخ الانتهاء],
    po.Status AS [الحالة]
FROM ((ProductionOrders po 
INNER JOIN Products p ON po.ProductID = p.ProductID)
LEFT JOIN Recipes r ON po.RecipeID = r.RecipeID)
ORDER BY po.StartDate DESC
"@
    }
    
    Write-Host "بدء إنشاء الاستعلامات..." -ForegroundColor Yellow
    
    $successCount = 0
    $errorCount = 0
    
    foreach ($queryName in $queries.Keys) {
        try {
            $querySQL = $queries[$queryName]
            
            # حذف الاستعلام إذا كان موجوداً
            try {
                $access.DoCmd.DeleteObject(1, $queryName) # 1 = acQuery
            } catch {
                # تجاهل الخطأ إذا لم يكن الاستعلام موجوداً
            }
            
            # إنشاء الاستعلام الجديد
            $queryDef = $access.CurrentDb().CreateQueryDef($queryName, $querySQL)
            $successCount++
            Write-Host "   ✓ تم إنشاء الاستعلام: $queryName" -ForegroundColor Green
        }
        catch {
            $errorCount++
            Write-Host "   ✗ خطأ في إنشاء الاستعلام $queryName : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`nملخص إنشاء الاستعلامات:" -ForegroundColor Cyan
    Write-Host "   الاستعلامات المنشأة بنجاح: $successCount" -ForegroundColor Green
    Write-Host "   الأخطاء: $errorCount" -ForegroundColor $(if($errorCount -eq 0) {"Green"} else {"Red"})
    
    # إغلاق قاعدة البيانات
    $access.CloseCurrentDatabase()
    $access.Quit()
    
    Write-Host "`n=== تم إكمال إضافة الاستعلامات ===" -ForegroundColor Green
    
    if ($errorCount -eq 0) {
        Write-Host "جميع الاستعلامات تم إنشاؤها بنجاح!" -ForegroundColor Green
        Write-Host "يمكنك الآن رؤية الاستعلامات في Access في قسم Queries" -ForegroundColor Yellow
    } else {
        Write-Host "تم إنشاء معظم الاستعلامات، راجع الأخطاء أعلاه" -ForegroundColor Yellow
    }
    
} catch {
    Write-Error "حدث خطأ عام: $($_.Exception.Message)"
} finally {
    # تنظيف الكائنات
    if ($access) {
        try {
            $access.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        } catch {
            # تجاهل أخطاء التنظيف
        }
    }
}
