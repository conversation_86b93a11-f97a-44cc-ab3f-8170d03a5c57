# سكريبت PowerShell لتنفيذ الاختبارات المتقدمة لنظام إدارة المصنع

param(
    [string]$TestCategory = "All",  # All, Tables, Relationships, Queries, Scenarios, Performance, Errors
    [switch]$Detailed = $false,     # عرض تفاصيل إضافية
    [switch]$StopOnError = $false   # التوقف عند أول خطأ
)

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

# متغيرات النتائج
$global:TestResults = @()
$global:PassedTests = 0
$global:FailedTests = 0
$global:TotalTests = 0

# دالة لتسجيل نتائج الاختبار
function Log-TestResult {
    param(
        [string]$TestName,
        [string]$Category,
        [bool]$Passed,
        [string]$Details = "",
        [string]$ExpectedResult = "",
        [string]$ActualResult = "",
        [int]$ExecutionTime = 0
    )
    
    $global:TotalTests++
    if ($Passed) {
        $global:PassedTests++
        $status = "✅ نجح"
        $color = "Green"
    } else {
        $global:FailedTests++
        $status = "❌ فشل"
        $color = "Red"
    }
    
    $result = [PSCustomObject]@{
        TestName = $TestName
        Category = $Category
        Status = $status
        Passed = $Passed
        Details = $Details
        ExpectedResult = $ExpectedResult
        ActualResult = $ActualResult
        ExecutionTime = $ExecutionTime
        Timestamp = Get-Date
    }
    
    $global:TestResults += $result
    
    Write-Host "[$Category] $TestName : $status" -ForegroundColor $color
    if ($Detailed -and $Details) {
        Write-Host "   التفاصيل: $Details" -ForegroundColor Gray
    }
    
    if (-not $Passed -and $StopOnError) {
        throw "توقف الاختبار بسبب فشل: $TestName"
    }
}

# دالة لتنفيذ استعلام SQL وقياس الوقت
function Execute-SQLQuery {
    param(
        [object]$Connection,
        [string]$Query,
        [bool]$ExpectError = $false
    )
    
    try {
        $startTime = Get-Date
        $recordset = $Connection.Execute($Query)
        $endTime = Get-Date
        $executionTime = ($endTime - $startTime).TotalMilliseconds
        
        $result = @{
            Success = $true
            RecordSet = $recordset
            ExecutionTime = $executionTime
            Error = $null
        }
        
        if ($ExpectError) {
            return @{
                Success = $false
                RecordSet = $null
                ExecutionTime = $executionTime
                Error = "كان متوقعاً حدوث خطأ ولكن الاستعلام نجح"
            }
        }
        
        return $result
    }
    catch {
        $endTime = Get-Date
        $executionTime = ($endTime - $startTime).TotalMilliseconds
        
        $result = @{
            Success = $false
            RecordSet = $null
            ExecutionTime = $executionTime
            Error = $_.Exception.Message
        }
        
        if ($ExpectError) {
            $result.Success = $true
        }
        
        return $result
    }
}

# دالة اختبارات الجداول والبيانات
function Test-TablesAndData {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات الجداول والبيانات ===" -ForegroundColor Yellow
    
    # اختبار 1.1.1: فحص وجود الجداول
    $query = "SELECT COUNT(*) FROM MSysObjects WHERE Type = 1 AND Name NOT LIKE 'MSys*'"
    $result = Execute-SQLQuery -Connection $Connection -Query $query
    
    if ($result.Success) {
        $tableCount = $result.RecordSet.Fields(0).Value
        $passed = ($tableCount -eq 20)
        Log-TestResult -TestName "فحص وجود الجداول" -Category "الجداول" -Passed $passed `
            -ExpectedResult "20 جدول" -ActualResult "$tableCount جدول" `
            -ExecutionTime $result.ExecutionTime
    } else {
        Log-TestResult -TestName "فحص وجود الجداول" -Category "الجداول" -Passed $false `
            -Details $result.Error
    }
    
    # اختبار 1.1.2: فحص البيانات الأساسية
    $basicDataTests = @(
        @{Table = "Units"; Expected = 7; Name = "وحدات القياس"},
        @{Table = "Categories"; Expected = 9; Name = "الفئات"},
        @{Table = "ProductionStages"; Expected = 8; Name = "مراحل الإنتاج"},
        @{Table = "Settings"; Expected = 8; Name = "الإعدادات"}
    )
    
    foreach ($test in $basicDataTests) {
        $query = "SELECT COUNT(*) FROM $($test.Table)"
        $result = Execute-SQLQuery -Connection $Connection -Query $query
        
        if ($result.Success) {
            $count = $result.RecordSet.Fields(0).Value
            $passed = ($count -ge $test.Expected)
            Log-TestResult -TestName "فحص بيانات $($test.Name)" -Category "البيانات الأساسية" -Passed $passed `
                -ExpectedResult "≥ $($test.Expected)" -ActualResult "$count" `
                -ExecutionTime $result.ExecutionTime
        } else {
            Log-TestResult -TestName "فحص بيانات $($test.Name)" -Category "البيانات الأساسية" -Passed $false `
                -Details $result.Error
        }
    }
    
    # اختبار 1.2.1: اختبار المفاتيح الأساسية (محاولة إدراج مكرر)
    $query = "INSERT INTO Units (UnitID, UnitName) VALUES (1, 'وحدة مكررة')"
    $result = Execute-SQLQuery -Connection $Connection -Query $query -ExpectError $true
    
    Log-TestResult -TestName "اختبار المفتاح الأساسي المكرر" -Category "القيود" -Passed $result.Success `
        -ExpectedResult "رفض الإدراج" -ActualResult $(if($result.Success) {"تم رفض الإدراج"} else {"تم قبول الإدراج"}) `
        -ExecutionTime $result.ExecutionTime
    
    # اختبار 1.2.2: اختبار الحقول المطلوبة
    $query = "INSERT INTO Suppliers (SupplierName, Phone) VALUES (NULL, '123456789')"
    $result = Execute-SQLQuery -Connection $Connection -Query $query -ExpectError $true
    
    Log-TestResult -TestName "اختبار الحقول المطلوبة" -Category "القيود" -Passed $result.Success `
        -ExpectedResult "رفض الإدراج" -ActualResult $(if($result.Success) {"تم رفض الإدراج"} else {"تم قبول الإدراج"}) `
        -ExecutionTime $result.ExecutionTime
}

# دالة اختبارات العلاقات
function Test-Relationships {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات العلاقات بين الجداول ===" -ForegroundColor Yellow
    
    # اختبار 2.1.2: اختبار سلامة المراجع
    $query = "INSERT INTO RawMaterials (MaterialName, CategoryID, UnitID, SupplierID) VALUES ('مادة اختبار', 999, 999, 999)"
    $result = Execute-SQLQuery -Connection $Connection -Query $query -ExpectError $true
    
    Log-TestResult -TestName "اختبار سلامة المراجع" -Category "العلاقات" -Passed $result.Success `
        -ExpectedResult "رفض الإدراج" -ActualResult $(if($result.Success) {"تم رفض الإدراج"} else {"تم قبول الإدراج"}) `
        -ExecutionTime $result.ExecutionTime
    
    # اختبار 2.2.1: اختبار منع الحذف
    $query = "DELETE FROM Suppliers WHERE SupplierID = 1"
    $result = Execute-SQLQuery -Connection $Connection -Query $query -ExpectError $true
    
    Log-TestResult -TestName "اختبار منع حذف السجل المرتبط" -Category "العلاقات" -Passed $result.Success `
        -ExpectedResult "منع الحذف" -ActualResult $(if($result.Success) {"تم منع الحذف"} else {"تم السماح بالحذف"}) `
        -ExecutionTime $result.ExecutionTime
}

# دالة اختبارات الاستعلامات
function Test-Queries {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات الاستعلامات المتقدمة ===" -ForegroundColor Yellow
    
    # قائمة الاستعلامات للاختبار
    $queries = @(
        "vw_RawMaterialsDetails",
        "vw_ProductsDetails", 
        "vw_RecipeDetails",
        "vw_RecipeTotalCost",
        "vw_PurchaseOrdersDetails",
        "vw_SalesInvoicesDetails",
        "qry_LowStockMaterials",
        "qry_LowStockProducts",
        "qry_MonthlyPurchases",
        "qry_MonthlySales",
        "qry_TopCustomers",
        "qry_TopProducts",
        "qry_ProductionOrdersDetails"
    )
    
    foreach ($queryName in $queries) {
        $query = "SELECT COUNT(*) FROM $queryName"
        $result = Execute-SQLQuery -Connection $Connection -Query $query
        
        if ($result.Success) {
            $count = $result.RecordSet.Fields(0).Value
            $passed = ($result.ExecutionTime -lt 5000) # أقل من 5 ثواني
            Log-TestResult -TestName "اختبار $queryName" -Category "الاستعلامات" -Passed $passed `
                -ExpectedResult "تنفيذ ناجح < 5 ثواني" -ActualResult "$count سجل في $($result.ExecutionTime) مللي ثانية" `
                -ExecutionTime $result.ExecutionTime
        } else {
            Log-TestResult -TestName "اختبار $queryName" -Category "الاستعلامات" -Passed $false `
                -Details $result.Error
        }
    }
    
    # اختبار دقة حساب تكلفة الوصفة
    $query = @"
SELECT 
    r.RecipeName,
    SUM(ri.Quantity * rm.UnitCost) AS TotalCost
FROM Recipes r
INNER JOIN RecipeIngredients ri ON r.RecipeID = ri.RecipeID
INNER JOIN RawMaterials rm ON ri.MaterialID = rm.MaterialID
WHERE r.RecipeID = 1
GROUP BY r.RecipeName
"@
    
    $result = Execute-SQLQuery -Connection $Connection -Query $query
    
    if ($result.Success -and -not $result.RecordSet.EOF) {
        $calculatedCost = $result.RecordSet.Fields("TotalCost").Value
        
        # مقارنة مع استعلام vw_RecipeTotalCost
        $query2 = "SELECT [إجمالي التكلفة] FROM vw_RecipeTotalCost WHERE RecipeID = 1"
        $result2 = Execute-SQLQuery -Connection $Connection -Query $query2
        
        if ($result2.Success -and -not $result2.RecordSet.EOF) {
            $viewCost = $result2.RecordSet.Fields(0).Value
            $passed = (([math]::Abs($calculatedCost - $viewCost)) -lt 0.01)
            
            Log-TestResult -TestName "دقة حساب تكلفة الوصفة" -Category "الحسابات" -Passed $passed `
                -ExpectedResult "$calculatedCost" -ActualResult "$viewCost" `
                -ExecutionTime ($result.ExecutionTime + $result2.ExecutionTime)
        }
    }
}

# دالة اختبارات السيناريوهات العملية
function Test-BusinessScenarios {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات السيناريوهات العملية ===" -ForegroundColor Yellow
    
    try {
        # سيناريو 1: دورة شراء كاملة
        Write-Host "   تنفيذ سيناريو دورة الشراء..." -ForegroundColor Cyan
        
        # 1. إنشاء طلب شراء
        $orderNumber = "PO-TEST-" + (Get-Date -Format "yyyyMMddHHmmss")
        $query = "INSERT INTO PurchaseOrders (OrderNumber, SupplierID, OrderDate, Status) VALUES ('$orderNumber', 1, Date(), 'Pending')"
        $result = Execute-SQLQuery -Connection $Connection -Query $query
        
        if ($result.Success) {
            # الحصول على ID الطلب
            $getOrderIdQuery = "SELECT OrderID FROM PurchaseOrders WHERE OrderNumber = '$orderNumber'"
            $orderResult = Execute-SQLQuery -Connection $Connection -Query $getOrderIdQuery
            
            if ($orderResult.Success -and -not $orderResult.RecordSet.EOF) {
                $orderId = $orderResult.RecordSet.Fields("OrderID").Value
                
                # 2. إضافة تفاصيل الطلب
                $detailQuery = "INSERT INTO PurchaseOrderDetails (OrderID, MaterialID, Quantity, UnitPrice) VALUES ($orderId, 1, 100, 5.50)"
                $detailResult = Execute-SQLQuery -Connection $Connection -Query $detailQuery
                
                $passed = $detailResult.Success
                Log-TestResult -TestName "سيناريو دورة الشراء - إنشاء الطلب" -Category "السيناريوهات" -Passed $passed `
                    -Details "طلب رقم: $orderNumber"
                
                if ($passed) {
                    # 3. التحقق من ظهور الطلب في الاستعلام
                    $checkQuery = "SELECT COUNT(*) FROM vw_PurchaseOrdersDetails WHERE [رقم الطلب] = '$orderNumber'"
                    $checkResult = Execute-SQLQuery -Connection $Connection -Query $checkQuery
                    
                    if ($checkResult.Success) {
                        $count = $checkResult.RecordSet.Fields(0).Value
                        $viewPassed = ($count -gt 0)
                        Log-TestResult -TestName "سيناريو دورة الشراء - ظهور في الاستعلام" -Category "السيناريوهات" -Passed $viewPassed `
                            -ExpectedResult "> 0" -ActualResult "$count"
                    }
                }
            }
        } else {
            Log-TestResult -TestName "سيناريو دورة الشراء" -Category "السيناريوهات" -Passed $false `
                -Details $result.Error
        }
        
        # سيناريو 2: اختبار تحديث المخزون
        Write-Host "   تنفيذ سيناريو تحديث المخزون..." -ForegroundColor Cyan
        
        # الحصول على المخزون الحالي
        $stockQuery = "SELECT CurrentStock FROM RawMaterials WHERE MaterialID = 1"
        $stockResult = Execute-SQLQuery -Connection $Connection -Query $stockQuery
        
        if ($stockResult.Success -and -not $stockResult.RecordSet.EOF) {
            $currentStock = $stockResult.RecordSet.Fields("CurrentStock").Value
            
            # تحديث المخزون
            $updateQuery = "UPDATE RawMaterials SET CurrentStock = CurrentStock + 50 WHERE MaterialID = 1"
            $updateResult = Execute-SQLQuery -Connection $Connection -Query $updateQuery
            
            if ($updateResult.Success) {
                # التحقق من التحديث
                $newStockResult = Execute-SQLQuery -Connection $Connection -Query $stockQuery
                
                if ($newStockResult.Success -and -not $newStockResult.RecordSet.EOF) {
                    $newStock = $newStockResult.RecordSet.Fields("CurrentStock").Value
                    $passed = ($newStock -eq ($currentStock + 50))
                    
                    Log-TestResult -TestName "سيناريو تحديث المخزون" -Category "السيناريوهات" -Passed $passed `
                        -ExpectedResult "$($currentStock + 50)" -ActualResult "$newStock"
                    
                    # إعادة المخزون لحالته الأصلية
                    $restoreQuery = "UPDATE RawMaterials SET CurrentStock = $currentStock WHERE MaterialID = 1"
                    Execute-SQLQuery -Connection $Connection -Query $restoreQuery | Out-Null
                }
            }
        }
        
    } catch {
        Log-TestResult -TestName "السيناريوهات العملية" -Category "السيناريوهات" -Passed $false `
            -Details $_.Exception.Message
    }
}

# دالة اختبارات الأداء
function Test-Performance {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات الأداء والضغط ===" -ForegroundColor Yellow
    
    # اختبار أداء الاستعلامات المعقدة
    $complexQueries = @(
        @{Name = "تفاصيل المواد الخام"; Query = "SELECT * FROM vw_RawMaterialsDetails"},
        @{Name = "تكلفة الوصفات"; Query = "SELECT * FROM vw_RecipeTotalCost"},
        @{Name = "تفاصيل المبيعات"; Query = "SELECT * FROM vw_SalesInvoicesDetails"}
    )
    
    foreach ($test in $complexQueries) {
        $result = Execute-SQLQuery -Connection $Connection -Query $test.Query
        
        if ($result.Success) {
            $passed = ($result.ExecutionTime -lt 2000) # أقل من 2 ثانية
            Log-TestResult -TestName "أداء استعلام $($test.Name)" -Category "الأداء" -Passed $passed `
                -ExpectedResult "< 2000 مللي ثانية" -ActualResult "$($result.ExecutionTime) مللي ثانية" `
                -ExecutionTime $result.ExecutionTime
        } else {
            Log-TestResult -TestName "أداء استعلام $($test.Name)" -Category "الأداء" -Passed $false `
                -Details $result.Error
        }
    }
    
    # اختبار إدراج متعدد
    Write-Host "   اختبار إدراج 100 سجل..." -ForegroundColor Cyan
    
    $startTime = Get-Date
    $successCount = 0
    
    for ($i = 1; $i -le 100; $i++) {
        $testName = "مورد اختبار أداء $i"
        $query = "INSERT INTO Suppliers (SupplierName, Phone, Email) VALUES ('$testName', '12345$i', 'test$<EMAIL>')"
        $result = Execute-SQLQuery -Connection $Connection -Query $query
        
        if ($result.Success) {
            $successCount++
        }
    }
    
    $endTime = Get-Date
    $totalTime = ($endTime - $startTime).TotalMilliseconds
    $passed = ($totalTime -lt 10000 -and $successCount -eq 100) # أقل من 10 ثواني و100% نجاح
    
    Log-TestResult -TestName "إدراج 100 سجل" -Category "الأداء" -Passed $passed `
        -ExpectedResult "100 سجل في < 10 ثواني" -ActualResult "$successCount سجل في $totalTime مللي ثانية" `
        -ExecutionTime $totalTime
    
    # تنظيف البيانات التجريبية
    $cleanupQuery = "DELETE FROM Suppliers WHERE SupplierName LIKE 'مورد اختبار أداء%'"
    Execute-SQLQuery -Connection $Connection -Query $cleanupQuery | Out-Null
}

# دالة اختبارات حالات الخطأ
function Test-ErrorCases {
    param([object]$Connection)
    
    Write-Host "`n=== اختبارات حالات الخطأ ===" -ForegroundColor Yellow
    
    # اختبار إدخال بيانات غير صحيحة
    $errorTests = @(
        @{
            Name = "نوع بيانات خاطئ"
            Query = "INSERT INTO RawMaterials (MaterialName, UnitCost) VALUES ('مادة اختبار', 'نص بدلاً من رقم')"
            ExpectError = $true
        },
        @{
            Name = "قيمة سالبة في المخزون"
            Query = "UPDATE RawMaterials SET CurrentStock = -100 WHERE MaterialID = 1"
            ExpectError = $false  # قد يكون مسموحاً حسب قواعد العمل
        },
        @{
            Name = "حذف فئة مرتبطة"
            Query = "DELETE FROM Categories WHERE CategoryID = 1"
            ExpectError = $true
        }
    )
    
    foreach ($test in $errorTests) {
        $result = Execute-SQLQuery -Connection $Connection -Query $test.Query -ExpectError $test.ExpectError
        
        Log-TestResult -TestName $test.Name -Category "حالات الخطأ" -Passed $result.Success `
            -ExpectedResult $(if($test.ExpectError) {"خطأ متوقع"} else {"تنفيذ ناجح"}) `
            -ActualResult $(if($result.Success -and $test.ExpectError) {"تم رفض العملية"} elseif($result.Success) {"تم قبول العملية"} else {"حدث خطأ"}) `
            -ExecutionTime $result.ExecutionTime
    }
}

# الدالة الرئيسية
function Main {
    try {
        Write-Host "=== بدء تنفيذ الاختبارات المتقدمة لنظام إدارة المصنع ===" -ForegroundColor Green
        Write-Host "فئة الاختبار: $TestCategory" -ForegroundColor Cyan
        Write-Host "التفاصيل: $($Detailed)" -ForegroundColor Cyan
        Write-Host "التوقف عند الخطأ: $($StopOnError)" -ForegroundColor Cyan
        
        # إنشاء اتصال بقاعدة البيانات
        $connection = New-Object -ComObject ADODB.Connection
        $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath")
        
        Write-Host "تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
        
        # تنفيذ الاختبارات حسب الفئة المحددة
        switch ($TestCategory.ToLower()) {
            "all" {
                Test-TablesAndData -Connection $connection
                Test-Relationships -Connection $connection
                Test-Queries -Connection $connection
                Test-BusinessScenarios -Connection $connection
                Test-Performance -Connection $connection
                Test-ErrorCases -Connection $connection
            }
            "tables" { Test-TablesAndData -Connection $connection }
            "relationships" { Test-Relationships -Connection $connection }
            "queries" { Test-Queries -Connection $connection }
            "scenarios" { Test-BusinessScenarios -Connection $connection }
            "performance" { Test-Performance -Connection $connection }
            "errors" { Test-ErrorCases -Connection $connection }
            default {
                Write-Error "فئة اختبار غير صحيحة: $TestCategory"
                return
            }
        }
        
        # إغلاق الاتصال
        $connection.Close()
        
        # عرض ملخص النتائج
        Write-Host "`n=== ملخص نتائج الاختبارات ===" -ForegroundColor Green
        Write-Host "إجمالي الاختبارات: $global:TotalTests" -ForegroundColor White
        Write-Host "الاختبارات الناجحة: $global:PassedTests" -ForegroundColor Green
        Write-Host "الاختبارات الفاشلة: $global:FailedTests" -ForegroundColor Red
        Write-Host "نسبة النجاح: $([math]::Round(($global:PassedTests / $global:TotalTests) * 100, 2))%" -ForegroundColor Cyan
        
        # حفظ النتائج في ملف
        $reportPath = "تقرير_الاختبارات_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
        $global:TestResults | Export-Csv -Path $reportPath -Encoding UTF8 -NoTypeInformation
        Write-Host "تم حفظ تقرير مفصل في: $reportPath" -ForegroundColor Yellow
        
        # عرض الاختبارات الفاشلة
        if ($global:FailedTests -gt 0) {
            Write-Host "`nالاختبارات الفاشلة:" -ForegroundColor Red
            $global:TestResults | Where-Object { -not $_.Passed } | ForEach-Object {
                Write-Host "   - $($_.TestName) [$($_.Category)]: $($_.Details)" -ForegroundColor Red
            }
        }
        
    } catch {
        Write-Error "حدث خطأ عام في تنفيذ الاختبارات: $($_.Exception.Message)"
    } finally {
        # تنظيف الكائنات
        if ($connection) {
            try {
                $connection.Close()
                [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
            } catch {
                # تجاهل أخطاء التنظيف
            }
        }
    }
}

# تنفيذ الدالة الرئيسية
Main
