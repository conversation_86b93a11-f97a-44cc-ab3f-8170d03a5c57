# سكريبت PowerShell لإنشاء قاعدة بيانات Access لنظام إدارة المصنع

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

# حذف قاعدة البيانات إذا كانت موجودة
if (Test-Path $dbPath) {
    Remove-Item $dbPath -Force
    Write-Host "تم حذف قاعدة البيانات الموجودة"
}

try {
    # إنشاء كائن ADOX Catalog لإنشاء قاعدة البيانات
    $catalog = New-Object -ComObject ADOX.Catalog
    $catalog.Create("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath")
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح"
    
    # إنشاء اتصال بقاعدة البيانات
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath")
    
    Write-Host "تم الاتصال بقاعدة البيانات"
    
    # إنشاء جدول وحدات القياس
    $sql = @"
CREATE TABLE Units (
    UnitID AUTOINCREMENT PRIMARY KEY,
    UnitName TEXT(50) NOT NULL,
    UnitNameArabic TEXT(50) NOT NULL,
    Description TEXT(255)
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول وحدات القياس"
    
    # إدراج وحدات القياس الأساسية
    $units = @(
        @("Kilogram", "كيلوجرام", "وحدة قياس الوزن"),
        @("Gram", "جرام", "وحدة قياس الوزن الصغيرة"),
        @("Liter", "لتر", "وحدة قياس السوائل"),
        @("Milliliter", "مليلتر", "وحدة قياس السوائل الصغيرة"),
        @("Piece", "قطعة", "وحدة العد"),
        @("Box", "صندوق", "وحدة التعبئة"),
        @("Bag", "كيس", "وحدة التعبئة")
    )
    
    foreach ($unit in $units) {
        $sql = "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('$($unit[0])', '$($unit[1])', '$($unit[2])')"
        $connection.Execute($sql)
    }
    Write-Host "تم إدراج وحدات القياس الأساسية"
    
    # إنشاء جدول فئات المنتجات والمواد
    $sql = @"
CREATE TABLE Categories (
    CategoryID AUTOINCREMENT PRIMARY KEY,
    CategoryName TEXT(100) NOT NULL,
    CategoryType TEXT(20) NOT NULL,
    Description TEXT(255)
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول الفئات"
    
    # إدراج فئات المواد الخام والمنتجات
    $categories = @(
        @("دقيق ومواد أساسية", "RawMaterial", "الدقيق والمواد الأساسية للخبز"),
        @("مكسرات", "RawMaterial", "الجوز واللوز والفستق وغيرها"),
        @("عسل ومحليات", "RawMaterial", "العسل والسكر والمحليات الطبيعية"),
        @("زيوت ودهون", "RawMaterial", "الزيوت والزبدة والسمن"),
        @("توابل ونكهات", "RawMaterial", "الفانيليا والقرفة وغيرها"),
        @("مواد تعبئة", "RawMaterial", "الأكياس والصناديق والملصقات"),
        @("معمول بالجوز", "Product", "معمول محشو بالجوز"),
        @("معمول بالعسل", "Product", "معمول محشو بالعسل"),
        @("حلويات أخرى", "Product", "منتجات حلويات متنوعة")
    )
    
    foreach ($category in $categories) {
        $sql = "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('$($category[0])', '$($category[1])', '$($category[2])')"
        $connection.Execute($sql)
    }
    Write-Host "تم إدراج الفئات الأساسية"
    
    # إنشاء جدول الموردين
    $sql = @"
CREATE TABLE Suppliers (
    SupplierID AUTOINCREMENT PRIMARY KEY,
    SupplierName TEXT(100) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(255),
    City TEXT(50),
    Country TEXT(50),
    TaxNumber TEXT(50),
    Rating INTEGER DEFAULT 0,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول الموردين"
    
    # إنشاء جدول المواد الخام
    $sql = @"
CREATE TABLE RawMaterials (
    MaterialID AUTOINCREMENT PRIMARY KEY,
    MaterialCode TEXT(20) NOT NULL,
    MaterialName TEXT(100) NOT NULL,
    CategoryID INTEGER,
    UnitID INTEGER NOT NULL,
    MinStockLevel DOUBLE DEFAULT 0,
    MaxStockLevel DOUBLE DEFAULT 0,
    CurrentStock DOUBLE DEFAULT 0,
    UnitCost CURRENCY DEFAULT 0,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now(),
    Description MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول المواد الخام"
    
    # إنشاء جدول المنتجات النهائية
    $sql = @"
CREATE TABLE Products (
    ProductID AUTOINCREMENT PRIMARY KEY,
    ProductCode TEXT(20) NOT NULL,
    ProductName TEXT(100) NOT NULL,
    CategoryID INTEGER,
    UnitID INTEGER NOT NULL,
    MinStockLevel DOUBLE DEFAULT 0,
    MaxStockLevel DOUBLE DEFAULT 0,
    CurrentStock DOUBLE DEFAULT 0,
    SalePrice CURRENCY DEFAULT 0,
    ProductionCost CURRENCY DEFAULT 0,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now(),
    Description MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول المنتجات النهائية"
    
    # إنشاء جدول الوصفات
    $sql = @"
CREATE TABLE Recipes (
    RecipeID AUTOINCREMENT PRIMARY KEY,
    ProductID INTEGER NOT NULL,
    RecipeName TEXT(100) NOT NULL,
    BatchSize DOUBLE NOT NULL,
    PreparationTime INTEGER,
    CookingTime INTEGER,
    TotalTime INTEGER,
    Instructions MEMO,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now()
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول الوصفات"
    
    # إنشاء جدول مكونات الوصفات
    $sql = @"
CREATE TABLE RecipeIngredients (
    IngredientID AUTOINCREMENT PRIMARY KEY,
    RecipeID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitID INTEGER NOT NULL,
    Notes TEXT(255)
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول مكونات الوصفات"
    
    # إنشاء جدول طلبات الشراء
    $sql = @"
CREATE TABLE PurchaseOrders (
    PurchaseOrderID AUTOINCREMENT PRIMARY KEY,
    OrderNumber TEXT(20) NOT NULL,
    SupplierID INTEGER NOT NULL,
    OrderDate DATETIME DEFAULT Now(),
    RequiredDate DATETIME,
    Status TEXT(20) DEFAULT 'Pending',
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    CreatedBy TEXT(50),
    CreatedDate DATETIME DEFAULT Now(),
    ApprovedBy TEXT(50),
    ApprovedDate DATETIME,
    Notes MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول طلبات الشراء"
    
    # إنشاء جدول تفاصيل طلبات الشراء
    $sql = @"
CREATE TABLE PurchaseOrderDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    PurchaseOrderID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    ReceivedQuantity DOUBLE DEFAULT 0,
    Notes TEXT(255)
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول تفاصيل طلبات الشراء"
    
    # إنشاء جدول فواتير الشراء
    $sql = @"
CREATE TABLE PurchaseInvoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    InvoiceNumber TEXT(20) NOT NULL,
    SupplierID INTEGER NOT NULL,
    PurchaseOrderID INTEGER,
    InvoiceDate DATETIME DEFAULT Now(),
    DueDate DATETIME,
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    Status TEXT(20) DEFAULT 'Unpaid',
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول فواتير الشراء"
    
    # إنشاء جدول تفاصيل فواتير الشراء
    $sql = @"
CREATE TABLE PurchaseInvoiceDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    InvoiceID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    ExpiryDate DATETIME,
    BatchNumber TEXT(50),
    Notes TEXT(255)
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول تفاصيل فواتير الشراء"
    
    # إنشاء جدول حركات المخزون
    $sql = @"
CREATE TABLE InventoryMovements (
    MovementID AUTOINCREMENT PRIMARY KEY,
    MovementDate DATETIME DEFAULT Now(),
    MovementType TEXT(20) NOT NULL,
    ItemType TEXT(20) NOT NULL,
    ItemID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitCost CURRENCY DEFAULT 0,
    TotalCost CURRENCY DEFAULT 0,
    ReferenceType TEXT(20),
    ReferenceID INTEGER,
    Notes TEXT(255),
    CreatedBy TEXT(50),
    CreatedDate DATETIME DEFAULT Now()
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول حركات المخزون"
    
    # إنشاء جدول العملاء
    $sql = @"
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerCode TEXT(20) NOT NULL,
    CustomerName TEXT(100) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(255),
    City TEXT(50),
    Country TEXT(50),
    TaxNumber TEXT(50),
    CreditLimit CURRENCY DEFAULT 0,
    CurrentBalance CURRENCY DEFAULT 0,
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
)
"@
    $connection.Execute($sql)
    Write-Host "تم إنشاء جدول العملاء"
    
    # إغلاق الاتصال
    $connection.Close()
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح في: $dbPath" -ForegroundColor Green
    
} catch {
    Write-Error "حدث خطأ أثناء إنشاء قاعدة البيانات: $($_.Exception.Message)"
} finally {
    # تنظيف الكائنات
    if ($connection) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
    }
    if ($catalog) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($catalog) | Out-Null
    }
}
