' سكريبت VBScript لإنشاء قاعدة بيانات Access لنظام إدارة المصنع
' يجب تشغيل هذا السكريبت من خلال Windows Script Host

Dim accessApp, db, strDBPath

' تحديد مسار قاعدة البيانات
strDBPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

' إنشاء كائن Access
Set accessApp = CreateObject("Access.Application")

' إنشاء قاعدة بيانات جديدة
accessApp.NewCurrentDatabase strDBPath

' الحصول على مرجع لقاعدة البيانات
Set db = accessApp.CurrentDb

' إنشاء جدول وحدات القياس
db.Execute "CREATE TABLE Units (" & _
    "UnitID AUTOINCREMENT PRIMARY KEY, " & _
    "UnitName TEXT(50) NOT NULL, " & _
    "UnitNameArabic TEXT(50) NOT NULL, " & _
    "Description TEXT(255)" & _
    ")"

' إدراج وحدات القياس الأساسية
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Kilogram', 'كيلوجرام', 'وحدة قياس الوزن')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Gram', 'جرام', 'وحدة قياس الوزن الصغيرة')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Liter', 'لتر', 'وحدة قياس السوائل')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Milliliter', 'مليلتر', 'وحدة قياس السوائل الصغيرة')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Piece', 'قطعة', 'وحدة العد')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Box', 'صندوق', 'وحدة التعبئة')"
db.Execute "INSERT INTO Units (UnitName, UnitNameArabic, Description) VALUES ('Bag', 'كيس', 'وحدة التعبئة')"

' إنشاء جدول فئات المنتجات والمواد
db.Execute "CREATE TABLE Categories (" & _
    "CategoryID AUTOINCREMENT PRIMARY KEY, " & _
    "CategoryName TEXT(100) NOT NULL, " & _
    "CategoryType TEXT(20) NOT NULL, " & _
    "Description TEXT(255)" & _
    ")"

' إدراج فئات المواد الخام
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('دقيق ومواد أساسية', 'RawMaterial', 'الدقيق والمواد الأساسية للخبز')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('مكسرات', 'RawMaterial', 'الجوز واللوز والفستق وغيرها')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('عسل ومحليات', 'RawMaterial', 'العسل والسكر والمحليات الطبيعية')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('زيوت ودهون', 'RawMaterial', 'الزيوت والزبدة والسمن')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('توابل ونكهات', 'RawMaterial', 'الفانيليا والقرفة وغيرها')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('مواد تعبئة', 'RawMaterial', 'الأكياس والصناديق والملصقات')"

' إدراج فئات المنتجات النهائية
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('معمول بالجوز', 'Product', 'معمول محشو بالجوز')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('معمول بالعسل', 'Product', 'معمول محشو بالعسل')"
db.Execute "INSERT INTO Categories (CategoryName, CategoryType, Description) VALUES ('حلويات أخرى', 'Product', 'منتجات حلويات متنوعة')"

' إنشاء جدول الموردين
db.Execute "CREATE TABLE Suppliers (" & _
    "SupplierID AUTOINCREMENT PRIMARY KEY, " & _
    "SupplierName TEXT(100) NOT NULL, " & _
    "ContactPerson TEXT(100), " & _
    "Phone TEXT(20), " & _
    "Mobile TEXT(20), " & _
    "Email TEXT(100), " & _
    "Address TEXT(255), " & _
    "City TEXT(50), " & _
    "Country TEXT(50), " & _
    "TaxNumber TEXT(50), " & _
    "Rating INTEGER DEFAULT 0, " & _
    "IsActive YESNO DEFAULT True, " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "Notes MEMO" & _
    ")"

' إنشاء جدول المواد الخام
db.Execute "CREATE TABLE RawMaterials (" & _
    "MaterialID AUTOINCREMENT PRIMARY KEY, " & _
    "MaterialCode TEXT(20) NOT NULL, " & _
    "MaterialName TEXT(100) NOT NULL, " & _
    "CategoryID INTEGER, " & _
    "UnitID INTEGER NOT NULL, " & _
    "MinStockLevel DOUBLE DEFAULT 0, " & _
    "MaxStockLevel DOUBLE DEFAULT 0, " & _
    "CurrentStock DOUBLE DEFAULT 0, " & _
    "UnitCost CURRENCY DEFAULT 0, " & _
    "IsActive YESNO DEFAULT True, " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "Description MEMO" & _
    ")"

' إنشاء جدول المنتجات النهائية
db.Execute "CREATE TABLE Products (" & _
    "ProductID AUTOINCREMENT PRIMARY KEY, " & _
    "ProductCode TEXT(20) NOT NULL, " & _
    "ProductName TEXT(100) NOT NULL, " & _
    "CategoryID INTEGER, " & _
    "UnitID INTEGER NOT NULL, " & _
    "MinStockLevel DOUBLE DEFAULT 0, " & _
    "MaxStockLevel DOUBLE DEFAULT 0, " & _
    "CurrentStock DOUBLE DEFAULT 0, " & _
    "SalePrice CURRENCY DEFAULT 0, " & _
    "ProductionCost CURRENCY DEFAULT 0, " & _
    "IsActive YESNO DEFAULT True, " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "Description MEMO" & _
    ")"

' إنشاء جدول الوصفات
db.Execute "CREATE TABLE Recipes (" & _
    "RecipeID AUTOINCREMENT PRIMARY KEY, " & _
    "ProductID INTEGER NOT NULL, " & _
    "RecipeName TEXT(100) NOT NULL, " & _
    "BatchSize DOUBLE NOT NULL, " & _
    "PreparationTime INTEGER, " & _
    "CookingTime INTEGER, " & _
    "TotalTime INTEGER, " & _
    "Instructions MEMO, " & _
    "IsActive YESNO DEFAULT True, " & _
    "CreatedDate DATETIME DEFAULT Now()" & _
    ")"

' إنشاء جدول مكونات الوصفات
db.Execute "CREATE TABLE RecipeIngredients (" & _
    "IngredientID AUTOINCREMENT PRIMARY KEY, " & _
    "RecipeID INTEGER NOT NULL, " & _
    "MaterialID INTEGER NOT NULL, " & _
    "Quantity DOUBLE NOT NULL, " & _
    "UnitID INTEGER NOT NULL, " & _
    "Notes TEXT(255)" & _
    ")"

' إنشاء جدول طلبات الشراء
db.Execute "CREATE TABLE PurchaseOrders (" & _
    "PurchaseOrderID AUTOINCREMENT PRIMARY KEY, " & _
    "OrderNumber TEXT(20) NOT NULL, " & _
    "SupplierID INTEGER NOT NULL, " & _
    "OrderDate DATETIME DEFAULT Now(), " & _
    "RequiredDate DATETIME, " & _
    "Status TEXT(20) DEFAULT 'Pending', " & _
    "TotalAmount CURRENCY DEFAULT 0, " & _
    "TaxAmount CURRENCY DEFAULT 0, " & _
    "DiscountAmount CURRENCY DEFAULT 0, " & _
    "NetAmount CURRENCY DEFAULT 0, " & _
    "CreatedBy TEXT(50), " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "ApprovedBy TEXT(50), " & _
    "ApprovedDate DATETIME, " & _
    "Notes MEMO" & _
    ")"

' إنشاء جدول تفاصيل طلبات الشراء
db.Execute "CREATE TABLE PurchaseOrderDetails (" & _
    "DetailID AUTOINCREMENT PRIMARY KEY, " & _
    "PurchaseOrderID INTEGER NOT NULL, " & _
    "MaterialID INTEGER NOT NULL, " & _
    "Quantity DOUBLE NOT NULL, " & _
    "UnitPrice CURRENCY NOT NULL, " & _
    "TotalPrice CURRENCY NOT NULL, " & _
    "ReceivedQuantity DOUBLE DEFAULT 0, " & _
    "Notes TEXT(255)" & _
    ")"

' إنشاء جدول فواتير الشراء
db.Execute "CREATE TABLE PurchaseInvoices (" & _
    "InvoiceID AUTOINCREMENT PRIMARY KEY, " & _
    "InvoiceNumber TEXT(20) NOT NULL, " & _
    "SupplierID INTEGER NOT NULL, " & _
    "PurchaseOrderID INTEGER, " & _
    "InvoiceDate DATETIME DEFAULT Now(), " & _
    "DueDate DATETIME, " & _
    "TotalAmount CURRENCY DEFAULT 0, " & _
    "TaxAmount CURRENCY DEFAULT 0, " & _
    "DiscountAmount CURRENCY DEFAULT 0, " & _
    "NetAmount CURRENCY DEFAULT 0, " & _
    "PaidAmount CURRENCY DEFAULT 0, " & _
    "Status TEXT(20) DEFAULT 'Unpaid', " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "Notes MEMO" & _
    ")"

' إنشاء جدول تفاصيل فواتير الشراء
db.Execute "CREATE TABLE PurchaseInvoiceDetails (" & _
    "DetailID AUTOINCREMENT PRIMARY KEY, " & _
    "InvoiceID INTEGER NOT NULL, " & _
    "MaterialID INTEGER NOT NULL, " & _
    "Quantity DOUBLE NOT NULL, " & _
    "UnitPrice CURRENCY NOT NULL, " & _
    "TotalPrice CURRENCY NOT NULL, " & _
    "ExpiryDate DATETIME, " & _
    "BatchNumber TEXT(50), " & _
    "Notes TEXT(255)" & _
    ")"

' إنشاء جدول حركات المخزون
db.Execute "CREATE TABLE InventoryMovements (" & _
    "MovementID AUTOINCREMENT PRIMARY KEY, " & _
    "MovementDate DATETIME DEFAULT Now(), " & _
    "MovementType TEXT(20) NOT NULL, " & _
    "ItemType TEXT(20) NOT NULL, " & _
    "ItemID INTEGER NOT NULL, " & _
    "Quantity DOUBLE NOT NULL, " & _
    "UnitCost CURRENCY DEFAULT 0, " & _
    "TotalCost CURRENCY DEFAULT 0, " & _
    "ReferenceType TEXT(20), " & _
    "ReferenceID INTEGER, " & _
    "Notes TEXT(255), " & _
    "CreatedBy TEXT(50), " & _
    "CreatedDate DATETIME DEFAULT Now()" & _
    ")"

' إنشاء جدول العملاء
db.Execute "CREATE TABLE Customers (" & _
    "CustomerID AUTOINCREMENT PRIMARY KEY, " & _
    "CustomerCode TEXT(20) NOT NULL, " & _
    "CustomerName TEXT(100) NOT NULL, " & _
    "ContactPerson TEXT(100), " & _
    "Phone TEXT(20), " & _
    "Mobile TEXT(20), " & _
    "Email TEXT(100), " & _
    "Address TEXT(255), " & _
    "City TEXT(50), " & _
    "Country TEXT(50), " & _
    "TaxNumber TEXT(50), " & _
    "CreditLimit CURRENCY DEFAULT 0, " & _
    "CurrentBalance CURRENCY DEFAULT 0, " & _
    "IsActive YESNO DEFAULT True, " & _
    "CreatedDate DATETIME DEFAULT Now(), " & _
    "Notes MEMO" & _
    ")"

' حفظ قاعدة البيانات وإغلاق Access
accessApp.DoCmd.Save
accessApp.Quit

' تنظيف الكائنات
Set db = Nothing
Set accessApp = Nothing

WScript.Echo "تم إنشاء قاعدة البيانات بنجاح في: " & strDBPath
