تصميم الواجهة الرئيسية لنظام إدارة المصنع

## مواصفات النموذج الرئيسي

### الخصائص العامة:
- اسم النموذج: frm_MainMenu
- نوع النموذج: Single Form
- حجم النموذج: 1024 x 768 بكسل
- لون الخلفية: أبيض مع تدرج رمادي فاتح
- الخط: Arial Unicode MS, 12pt
- اتجاه النص: من اليمين لليسار (RTL)

### تخطيط الواجهة:

#### 1. الترويسة (Header):
- ارتفاع: 100 بكسل
- لون الخلفية: أزرق داكن (#2C3E50)
- المحتوى:
  * شعار الشركة (يسار)
  * عنوان النظام: "نظام إدارة مصنع المواد الغذائية" (وسط)
  * التاريخ والوقت الحالي (يمين)
  * اسم المستخدم الحالي (يمين)

#### 2. شريط التنقل الرئيسي (Navigation Bar):
- ارتفاع: 60 بكسل
- لون الخلفية: رمادي فاتح (#ECF0F1)
- أزرار التنقل الرئيسية:
  * الرئيسية
  * المشتريات
  * المخازن
  * الإنتاج
  * المبيعات
  * التقارير
  * الإعدادات

#### 3. المنطقة الرئيسية (Main Area):
- تقسيم إلى 3 أعمدة متساوية
- كل عمود يحتوي على بطاقات للوحدات المختلفة

##### العمود الأول - إدارة المشتريات والموردين:
```
┌─────────────────────────────────┐
│        إدارة الموردين          │
│  [أيقونة المورد]               │
│  • إضافة مورد جديد             │
│  • عرض قائمة الموردين          │
│  • تقييم الموردين              │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        طلبات الشراء            │
│  [أيقونة طلب الشراء]           │
│  • إنشاء طلب شراء جديد         │
│  • متابعة الطلبات الحالية      │
│  • طباعة طلبات الشراء          │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        فواتير الشراء           │
│  [أيقونة فاتورة الشراء]        │
│  • تسجيل فاتورة جديدة          │
│  • متابعة المدفوعات            │
│  • ربط بطلبات الشراء           │
│  [زر الدخول]                   │
└─────────────────────────────────┘
```

##### العمود الثاني - إدارة المخازن والإنتاج:
```
┌─────────────────────────────────┐
│        إدارة المواد الخام       │
│  [أيقونة المواد الخام]         │
│  • إضافة مادة خام جديدة        │
│  • تتبع مستويات المخزون        │
│  • تنبيهات المخزون المنخفض     │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        إدارة المنتجات          │
│  [أيقونة المنتجات]             │
│  • إضافة منتج جديد             │
│  • إدارة الأسعار والتكاليف     │
│  • تتبع مخزون المنتجات         │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        إدارة الوصفات           │
│  [أيقونة الوصفات]              │
│  • إنشاء وصفة جديدة            │
│  • حساب تكلفة الوصفات          │
│  • تعديل المكونات              │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        أوامر الإنتاج           │
│  [أيقونة الإنتاج]              │
│  • إنشاء أمر إنتاج جديد        │
│  • متابعة مراحل الإنتاج        │
│  • تحديث حالة الإنتاج          │
│  [زر الدخول]                   │
└─────────────────────────────────┘
```

##### العمود الثالث - إدارة المبيعات والتقارير:
```
┌─────────────────────────────────┐
│        إدارة العملاء           │
│  [أيقونة العملاء]              │
│  • إضافة عميل جديد             │
│  • متابعة الحسابات             │
│  • تاريخ التعامل               │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        فواتير المبيعات         │
│  [أيقونة فاتورة المبيعات]      │
│  • إنشاء فاتورة جديدة          │
│  • متابعة المستحقات            │
│  • طباعة الفواتير              │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        التقارير والتحليلات     │
│  [أيقونة التقارير]             │
│  • تقارير المخزون              │
│  • تقارير المبيعات             │
│  • تقارير الربحية              │
│  [زر الدخول]                   │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        إعدادات النظام          │
│  [أيقونة الإعدادات]            │
│  • إعدادات الشركة              │
│  • إعدادات المستخدمين          │
│  • النسخ الاحتياطي             │
│  [زر الدخول]                   │
└─────────────────────────────────┘
```

#### 4. لوحة المعلومات السريعة (Dashboard):
- موقع: أسفل البطاقات
- ارتفاع: 200 بكسل
- تقسيم إلى 4 أقسام:

```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ المخزون    │ الطلبات    │ الإنتاج    │ المبيعات   │
│ المنخفض    │ المعلقة     │ الجاري     │ اليوم      │
│             │             │             │             │
│ [عدد] مادة  │ [عدد] طلب  │ [عدد] أمر  │ [مبلغ] ر.س │
│ تحتاج إعادة │ في الانتظار│ قيد التنفيذ│ إجمالي     │
│ طلب        │             │             │ المبيعات   │
│             │             │             │             │
│ [عرض التفاصيل] [عرض التفاصيل] [عرض التفاصيل] [عرض التفاصيل] │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

#### 5. التذييل (Footer):
- ارتفاع: 40 بكسل
- لون الخلفية: رمادي داكن (#34495E)
- المحتوى:
  * حقوق الطبع والنشر (يسار)
  * إصدار النظام (وسط)
  * معلومات الاتصال (يمين)

### الألوان المستخدمة:
- الأزرق الداكن: #2C3E50 (الترويسة)
- الأزرق الفاتح: #3498DB (الأزرار الرئيسية)
- الرمادي الفاتح: #ECF0F1 (الخلفيات)
- الرمادي الداكن: #34495E (التذييل)
- الأخضر: #27AE60 (حالات النجاح)
- الأحمر: #E74C3C (التنبيهات)
- الأصفر: #F39C12 (التحذيرات)

### الأيقونات المطلوبة:
- مورد: 👥
- طلب شراء: 📋
- فاتورة شراء: 📄
- مواد خام: 📦
- منتجات: 🍯
- وصفات: 📖
- إنتاج: 🏭
- عملاء: 👤
- فاتورة مبيعات: 💰
- تقارير: 📊
- إعدادات: ⚙️

### التفاعل والأحداث:
1. **عند تحميل النموذج**:
   - تحديث التاريخ والوقت
   - تحديث لوحة المعلومات السريعة
   - فحص مستويات المخزون المنخفض

2. **عند النقر على بطاقة**:
   - فتح النموذج المقابل
   - تمرير المعاملات المطلوبة

3. **تحديث تلقائي**:
   - تحديث لوحة المعلومات كل 5 دقائق
   - تحديث التاريخ والوقت كل دقيقة

### كود VBA للنموذج الرئيسي:
```vba
Private Sub Form_Load()
    ' تحديث التاريخ والوقت
    Me.lblDateTime = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
    
    ' تحديث لوحة المعلومات
    UpdateDashboard
    
    ' بدء المؤقت للتحديث التلقائي
    Me.TimerInterval = 60000 ' دقيقة واحدة
End Sub

Private Sub UpdateDashboard()
    ' تحديث عدد المواد منخفضة المخزون
    Me.lblLowStock = DCount("*", "RawMaterials", "CurrentStock <= MinStockLevel")
    
    ' تحديث عدد الطلبات المعلقة
    Me.lblPendingOrders = DCount("*", "PurchaseOrders", "Status = 'Pending'")
    
    ' تحديث عدد أوامر الإنتاج الجارية
    Me.lblActiveProduction = DCount("*", "ProductionOrders", "Status = 'InProgress'")
    
    ' تحديث مبيعات اليوم
    Me.lblTodaySales = Nz(DSum("NetAmount", "SalesInvoices", "InvoiceDate = Date()"), 0)
End Sub

Private Sub Timer()
    ' تحديث التاريخ والوقت
    Me.lblDateTime = Format(Now(), "dd/mm/yyyy hh:nn AM/PM")
    
    ' تحديث لوحة المعلومات كل 5 دقائق
    Static Counter As Integer
    Counter = Counter + 1
    If Counter >= 5 Then
        UpdateDashboard
        Counter = 0
    End If
End Sub
```

### خطوات التنفيذ:
1. إنشاء نموذج فارغ جديد
2. ضبط خصائص النموذج (الحجم، الألوان، الخط)
3. إضافة عناصر التحكم (تسميات، أزرار، صور)
4. تنسيق التخطيط والألوان
5. إضافة كود VBA للأحداث
6. اختبار جميع الوظائف
7. ضبط التنسيق النهائي
