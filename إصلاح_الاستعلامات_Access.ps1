# سكريبت إصلاح الاستعلامات بصيغة Access الصحيحة

param(
    [string]$DatabasePath = "نظام_إدارة_المصنع.accdb"
)

Write-Host "=== بدء إصلاح الاستعلامات بصيغة Access ===" -ForegroundColor Green

try {
    # إنشاء اتصال بقاعدة البيانات
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$DatabasePath")
    
    Write-Host "تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء الاستعلامات باستخدام صيغة Access الصحيحة
    Write-Host "`nإنشاء الاستعلامات المصححة..." -ForegroundColor Yellow
    
    # 1. استعلام المواد الخام - بدون JOIN متعددة
    $sql1 = @"
SELECT 
    rm.MaterialID,
    rm.MaterialCode,
    rm.MaterialName,
    rm.CurrentStock,
    rm.MinStockLevel,
    rm.MaxStockLevel,
    rm.UnitCost,
    rm.CurrentStock * rm.UnitCost AS TotalValue,
    IIf(rm.CurrentStock <= rm.MinStockLevel, 'منخفض', 
        IIf(rm.CurrentStock >= rm.MaxStockLevel, 'مرتفع', 'طبيعي')) AS StockStatus
INTO vw_RawMaterialsDetails
FROM RawMaterials rm
WHERE rm.IsActive = True
"@
    
    try {
        # حذف الجدول إذا كان موجوداً
        try { $connection.Execute("DROP TABLE vw_RawMaterialsDetails") } catch {}
        
        $connection.Execute($sql1)
        Write-Host "✅ تم إنشاء vw_RawMaterialsDetails" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_RawMaterialsDetails: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 2. استعلام المنتجات
    $sql2 = @"
SELECT 
    p.ProductID,
    p.ProductCode,
    p.ProductName,
    p.CurrentStock,
    p.MinStockLevel,
    p.MaxStockLevel,
    p.SalePrice,
    p.ProductionCost,
    p.SalePrice - p.ProductionCost AS ProfitMargin,
    p.CurrentStock * p.SalePrice AS TotalValue,
    IIf(p.CurrentStock <= p.MinStockLevel, 'منخفض', 
        IIf(p.CurrentStock >= p.MaxStockLevel, 'مرتفع', 'طبيعي')) AS StockStatus
INTO vw_ProductsDetails_Fixed
FROM Products p
WHERE p.IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP TABLE vw_ProductsDetails_Fixed") } catch {}
        $connection.Execute($sql2)
        Write-Host "✅ تم إنشاء vw_ProductsDetails_Fixed" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_ProductsDetails_Fixed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 3. استعلام المواد منخفضة المخزون
    $sql3 = @"
SELECT 
    rm.MaterialID,
    rm.MaterialName,
    rm.CurrentStock,
    rm.MinStockLevel,
    rm.MinStockLevel - rm.CurrentStock AS QuantityNeeded,
    rm.UnitCost,
    (rm.MinStockLevel - rm.CurrentStock) * rm.UnitCost AS ReorderCost
INTO qry_LowStockMaterials_Fixed
FROM RawMaterials rm
WHERE rm.CurrentStock <= rm.MinStockLevel 
AND rm.IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP TABLE qry_LowStockMaterials_Fixed") } catch {}
        $connection.Execute($sql3)
        Write-Host "✅ تم إنشاء qry_LowStockMaterials_Fixed" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_LowStockMaterials_Fixed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 4. استعلام المنتجات منخفضة المخزون
    $sql4 = @"
SELECT 
    p.ProductID,
    p.ProductName,
    p.CurrentStock,
    p.MinStockLevel,
    p.MinStockLevel - p.CurrentStock AS QuantityNeeded,
    p.ProductionCost,
    (p.MinStockLevel - p.CurrentStock) * p.ProductionCost AS ProductionCost
INTO qry_LowStockProducts_Fixed
FROM Products p
WHERE p.CurrentStock <= p.MinStockLevel 
AND p.IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP TABLE qry_LowStockProducts_Fixed") } catch {}
        $connection.Execute($sql4)
        Write-Host "✅ تم إنشاء qry_LowStockProducts_Fixed" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_LowStockProducts_Fixed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 5. استعلام قيمة المخزون الإجمالية
    $sql5 = @"
SELECT 
    'المواد الخام' AS InventoryType,
    COUNT(*) AS ItemCount,
    SUM(rm.CurrentStock) AS TotalQuantity,
    SUM(rm.CurrentStock * rm.UnitCost) AS TotalValue
INTO qry_InventoryValue_RawMaterials
FROM RawMaterials rm
WHERE rm.IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP TABLE qry_InventoryValue_RawMaterials") } catch {}
        $connection.Execute($sql5)
        Write-Host "✅ تم إنشاء qry_InventoryValue_RawMaterials" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_InventoryValue_RawMaterials: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 6. استعلام قيمة مخزون المنتجات
    $sql6 = @"
SELECT 
    'المنتجات النهائية' AS InventoryType,
    COUNT(*) AS ItemCount,
    SUM(p.CurrentStock) AS TotalQuantity,
    SUM(p.CurrentStock * p.ProductionCost) AS TotalValue
INTO qry_InventoryValue_Products
FROM Products p
WHERE p.IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP TABLE qry_InventoryValue_Products") } catch {}
        $connection.Execute($sql6)
        Write-Host "✅ تم إنشاء qry_InventoryValue_Products" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء qry_InventoryValue_Products: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # الآن سنقوم بإنشاء استعلامات بسيطة تعمل مع Access
    Write-Host "`nإنشاء استعلامات بسيطة..." -ForegroundColor Yellow
    
    # استعلام بسيط للمواد الخام مع الفئات
    $simpleQuery1 = @"
CREATE VIEW vw_RawMaterials_Simple AS
SELECT 
    MaterialID,
    MaterialName,
    CurrentStock,
    UnitCost,
    CurrentStock * UnitCost AS TotalValue
FROM RawMaterials
WHERE IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP VIEW vw_RawMaterials_Simple") } catch {}
        $connection.Execute($simpleQuery1)
        Write-Host "✅ تم إنشاء vw_RawMaterials_Simple" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_RawMaterials_Simple: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # استعلام بسيط للمنتجات
    $simpleQuery2 = @"
CREATE VIEW vw_Products_Simple AS
SELECT 
    ProductID,
    ProductName,
    CurrentStock,
    SalePrice,
    ProductionCost,
    SalePrice - ProductionCost AS Profit
FROM Products
WHERE IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP VIEW vw_Products_Simple") } catch {}
        $connection.Execute($simpleQuery2)
        Write-Host "✅ تم إنشاء vw_Products_Simple" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_Products_Simple: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # استعلام بسيط للوصفات
    $simpleQuery3 = @"
CREATE VIEW vw_Recipes_Simple AS
SELECT 
    RecipeID,
    RecipeName,
    ProductID,
    ProducedQuantity,
    IsActive
FROM Recipes
WHERE IsActive = True
"@
    
    try {
        try { $connection.Execute("DROP VIEW vw_Recipes_Simple") } catch {}
        $connection.Execute($simpleQuery3)
        Write-Host "✅ تم إنشاء vw_Recipes_Simple" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_Recipes_Simple: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # استعلام بسيط لمكونات الوصفات
    $simpleQuery4 = @"
CREATE VIEW vw_RecipeIngredients_Simple AS
SELECT 
    ri.RecipeID,
    ri.MaterialID,
    ri.Quantity,
    rm.MaterialName,
    rm.UnitCost,
    ri.Quantity * rm.UnitCost AS IngredientCost
FROM RecipeIngredients ri, RawMaterials rm
WHERE ri.MaterialID = rm.MaterialID
"@
    
    try {
        try { $connection.Execute("DROP VIEW vw_RecipeIngredients_Simple") } catch {}
        $connection.Execute($simpleQuery4)
        Write-Host "✅ تم إنشاء vw_RecipeIngredients_Simple" -ForegroundColor Green
    } catch {
        Write-Host "❌ خطأ في إنشاء vw_RecipeIngredients_Simple: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n=== تم إكمال إصلاح الاستعلامات بنجاح ===" -ForegroundColor Green
    Write-Host "تم إنشاء استعلامات بسيطة تعمل مع Access" -ForegroundColor Cyan
    
    # عرض قائمة الاستعلامات الجديدة
    Write-Host "`nالاستعلامات الجديدة المتاحة:" -ForegroundColor Yellow
    Write-Host "- vw_RawMaterials_Simple" -ForegroundColor Green
    Write-Host "- vw_Products_Simple" -ForegroundColor Green  
    Write-Host "- vw_Recipes_Simple" -ForegroundColor Green
    Write-Host "- vw_RecipeIngredients_Simple" -ForegroundColor Green
    Write-Host "- qry_LowStockMaterials_Fixed (جدول)" -ForegroundColor Green
    Write-Host "- qry_LowStockProducts_Fixed (جدول)" -ForegroundColor Green
    Write-Host "- qry_InventoryValue_RawMaterials (جدول)" -ForegroundColor Green
    Write-Host "- qry_InventoryValue_Products (جدول)" -ForegroundColor Green
    
} catch {
    Write-Error "حدث خطأ عام: $($_.Exception.Message)"
} finally {
    # إغلاق الاتصال
    if ($connection) {
        try {
            $connection.Close()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
        } catch {
            # تجاهل أخطاء الإغلاق
        }
    }
}

Write-Host "`nلاختبار الإصلاحات، افتح Access وتحقق من الاستعلامات الجديدة" -ForegroundColor Yellow
