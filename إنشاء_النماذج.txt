تعليمات إنشاء النماذج في Microsoft Access:

1. النموذج الرئيسي (Main Menu):
   - إنشاء نموذج فارغ
   - إضافة أزرار للوحدات المختلفة:
     * إدارة الموردين
     * إدارة المواد الخام
     * إدارة المنتجات
     * إدارة الوصفات
     * طلبات الشراء
     * فواتير الشراء
     * أوامر الإنتاج
     * إدارة العملاء
     * فواتير المبيعات
     * التقارير
     * الإعدادات

2. نموذج إدارة الموردين:
   - استخدام معالج النماذج لجدول Suppliers
   - تخطيط عمودي مع جميع الحقول
   - إضافة أزرار: جديد، حفظ، حذف، بحث
   - تنسيق النص باللغة العربية

3. نموذج إدارة المواد الخام:
   - استخدام معالج النماذج لجدول RawMaterials
   - ربط CategoryID بجدول Categories
   - ربط UnitID بجدول Units
   - إضافة حقول محسوبة لحالة المخزون

4. نموذج إدارة المنتجات:
   - استخدام معالج النماذج لجدول Products
   - ربط CategoryID بجدول Categories
   - ربط UnitID بجدول Units
   - إضافة حقول محسوبة للربحية

5. نموذج إدارة الوصفات:
   - نموذج رئيسي لجدول Recipes
   - نموذج فرعي لجدول RecipeIngredients
   - ربط MaterialID بجدول RawMaterials
   - حساب التكلفة الإجمالية للوصفة

6. نموذج طلبات الشراء:
   - نموذج رئيسي لجدول PurchaseOrders
   - نموذج فرعي لجدول PurchaseOrderDetails
   - حساب المجاميع تلقائياً
   - إضافة أزرار للطباعة والإرسال

7. نموذج فواتير الشراء:
   - نموذج رئيسي لجدول PurchaseInvoices
   - نموذج فرعي لجدول PurchaseInvoiceDetails
   - ربط بطلبات الشراء
   - تحديث المخزون تلقائياً

8. نموذج أوامر الإنتاج:
   - نموذج رئيسي لجدول ProductionOrders
   - نموذج فرعي لمراحل الإنتاج
   - حساب المواد المطلوبة من الوصفة
   - تتبع حالة الإنتاج

9. نموذج إدارة العملاء:
   - استخدام معالج النماذج لجدول Customers
   - إضافة حقول محسوبة للرصيد والحد الائتماني
   - تاريخ آخر عملية شراء

10. نموذج فواتير المبيعات:
    - نموذج رئيسي لجدول SalesInvoices
    - نموذج فرعي لجدول SalesInvoiceDetails
    - حساب المجاميع والضرائب
    - تحديث المخزون تلقائياً

خطوات التنفيذ:
1. فتح قاعدة البيانات في Access
2. استخدام معالج النماذج لكل جدول
3. تخصيص التخطيط والتنسيق
4. إضافة الأزرار والوظائف
5. ضبط خصائص النماذج للغة العربية
6. اختبار جميع الوظائف

ملاحظات مهمة:
- استخدام الخط Arial Unicode MS للنصوص العربية
- ضبط اتجاه النص من اليمين لليسار
- إضافة رسائل تأكيد للعمليات المهمة
- استخدام ألوان متناسقة ومريحة للعين
- إضافة أيقونات واضحة للأزرار
