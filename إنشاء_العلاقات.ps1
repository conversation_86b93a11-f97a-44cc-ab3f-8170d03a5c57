# سكريبت PowerShell لإنشاء العلاقات بين الجداول في قاعدة البيانات

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

try {
    Write-Host "=== بدء إنشاء العلاقات بين الجداول ===" -ForegroundColor Green
    
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # فتح قاعدة البيانات
    $access.OpenCurrentDatabase($dbPath)
    
    Write-Host "تم فتح قاعدة البيانات بنجاح" -ForegroundColor Green
    
    # إنشاء العلاقات باستخدام SQL DDL
    $relationships = @(
        # العلاقة بين RawMaterials و Units
        "ALTER TABLE RawMaterials ADD CONSTRAINT FK_RawMaterials_Units FOREIGN KEY (UnitID) REFERENCES Units(UnitID)",
        
        # العلاقة بين RawMaterials و Categories
        "ALTER TABLE RawMaterials ADD CONSTRAINT FK_RawMaterials_Categories FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)",
        
        # العلاقة بين RawMaterials و Suppliers
        "ALTER TABLE RawMaterials ADD CONSTRAINT FK_RawMaterials_Suppliers FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)",
        
        # العلاقة بين Products و Units
        "ALTER TABLE Products ADD CONSTRAINT FK_Products_Units FOREIGN KEY (UnitID) REFERENCES Units(UnitID)",
        
        # العلاقة بين Products و Categories
        "ALTER TABLE Products ADD CONSTRAINT FK_Products_Categories FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID)",
        
        # العلاقة بين RecipeIngredients و Recipes
        "ALTER TABLE RecipeIngredients ADD CONSTRAINT FK_RecipeIngredients_Recipes FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID)",
        
        # العلاقة بين RecipeIngredients و RawMaterials
        "ALTER TABLE RecipeIngredients ADD CONSTRAINT FK_RecipeIngredients_RawMaterials FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID)",
        
        # العلاقة بين PurchaseOrders و Suppliers
        "ALTER TABLE PurchaseOrders ADD CONSTRAINT FK_PurchaseOrders_Suppliers FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)",
        
        # العلاقة بين PurchaseOrderDetails و PurchaseOrders
        "ALTER TABLE PurchaseOrderDetails ADD CONSTRAINT FK_PurchaseOrderDetails_PurchaseOrders FOREIGN KEY (OrderID) REFERENCES PurchaseOrders(OrderID)",
        
        # العلاقة بين PurchaseOrderDetails و RawMaterials
        "ALTER TABLE PurchaseOrderDetails ADD CONSTRAINT FK_PurchaseOrderDetails_RawMaterials FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID)",
        
        # العلاقة بين PurchaseInvoices و Suppliers
        "ALTER TABLE PurchaseInvoices ADD CONSTRAINT FK_PurchaseInvoices_Suppliers FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)",
        
        # العلاقة بين PurchaseInvoices و PurchaseOrders
        "ALTER TABLE PurchaseInvoices ADD CONSTRAINT FK_PurchaseInvoices_PurchaseOrders FOREIGN KEY (OrderID) REFERENCES PurchaseOrders(OrderID)",
        
        # العلاقة بين PurchaseInvoiceDetails و PurchaseInvoices
        "ALTER TABLE PurchaseInvoiceDetails ADD CONSTRAINT FK_PurchaseInvoiceDetails_PurchaseInvoices FOREIGN KEY (InvoiceID) REFERENCES PurchaseInvoices(InvoiceID)",
        
        # العلاقة بين PurchaseInvoiceDetails و RawMaterials
        "ALTER TABLE PurchaseInvoiceDetails ADD CONSTRAINT FK_PurchaseInvoiceDetails_RawMaterials FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID)",
        
        # العلاقة بين ProductionOrders و Products
        "ALTER TABLE ProductionOrders ADD CONSTRAINT FK_ProductionOrders_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID)",
        
        # العلاقة بين ProductionOrders و Recipes
        "ALTER TABLE ProductionOrders ADD CONSTRAINT FK_ProductionOrders_Recipes FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID)",
        
        # العلاقة بين ProductionOrderStages و ProductionOrders
        "ALTER TABLE ProductionOrderStages ADD CONSTRAINT FK_ProductionOrderStages_ProductionOrders FOREIGN KEY (ProductionOrderID) REFERENCES ProductionOrders(ProductionOrderID)",
        
        # العلاقة بين ProductionOrderStages و ProductionStages
        "ALTER TABLE ProductionOrderStages ADD CONSTRAINT FK_ProductionOrderStages_ProductionStages FOREIGN KEY (StageID) REFERENCES ProductionStages(StageID)",
        
        # العلاقة بين SalesInvoices و Customers
        "ALTER TABLE SalesInvoices ADD CONSTRAINT FK_SalesInvoices_Customers FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)",
        
        # العلاقة بين SalesInvoiceDetails و SalesInvoices
        "ALTER TABLE SalesInvoiceDetails ADD CONSTRAINT FK_SalesInvoiceDetails_SalesInvoices FOREIGN KEY (InvoiceID) REFERENCES SalesInvoices(InvoiceID)",
        
        # العلاقة بين SalesInvoiceDetails و Products
        "ALTER TABLE SalesInvoiceDetails ADD CONSTRAINT FK_SalesInvoiceDetails_Products FOREIGN KEY (ProductID) REFERENCES Products(ProductID)",
        
        # العلاقة بين Payments و SalesInvoices
        "ALTER TABLE Payments ADD CONSTRAINT FK_Payments_SalesInvoices FOREIGN KEY (InvoiceID) REFERENCES SalesInvoices(InvoiceID)",
        
        # العلاقة بين Payments و Customers
        "ALTER TABLE Payments ADD CONSTRAINT FK_Payments_Customers FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)"
    )
    
    Write-Host "بدء إنشاء العلاقات..." -ForegroundColor Yellow
    
    $successCount = 0
    $errorCount = 0
    
    foreach ($relationship in $relationships) {
        try {
            $access.CurrentDb().Execute($relationship)
            $successCount++
            
            # استخراج اسم العلاقة من SQL
            if ($relationship -match "CONSTRAINT\s+(\w+)") {
                $relationshipName = $matches[1]
                Write-Host "   ✓ تم إنشاء العلاقة: $relationshipName" -ForegroundColor Green
            }
        }
        catch {
            $errorCount++
            Write-Host "   ✗ خطأ في إنشاء العلاقة: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`nملخص إنشاء العلاقات:" -ForegroundColor Cyan
    Write-Host "   العلاقات المنشأة بنجاح: $successCount" -ForegroundColor Green
    Write-Host "   الأخطاء: $errorCount" -ForegroundColor $(if($errorCount -eq 0) {"Green"} else {"Red"})
    
    # إغلاق قاعدة البيانات
    $access.CloseCurrentDatabase()
    $access.Quit()
    
    Write-Host "`n=== تم إكمال إنشاء العلاقات ===" -ForegroundColor Green
    
    if ($errorCount -eq 0) {
        Write-Host "جميع العلاقات تم إنشاؤها بنجاح!" -ForegroundColor Green
        Write-Host "يمكنك الآن رؤية العلاقات في Access من خلال:" -ForegroundColor Yellow
        Write-Host "Database Tools > Relationships" -ForegroundColor White
    } else {
        Write-Host "تم إنشاء معظم العلاقات، راجع الأخطاء أعلاه" -ForegroundColor Yellow
    }
    
} catch {
    Write-Error "حدث خطأ عام: $($_.Exception.Message)"
} finally {
    # تنظيف الكائنات
    if ($access) {
        try {
            $access.Quit()
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        } catch {
            # تجاهل أخطاء التنظيف
        }
    }
}
