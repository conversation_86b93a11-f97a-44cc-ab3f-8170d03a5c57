# دليل إنشاء التقارير يدوياً في Microsoft Access

## نظرة عامة
الآن بعد أن أصبحت لديك الاستعلامات المتقدمة، يمكنك إنشاء التقارير بسهولة باستخدام معالج التقارير في Access.

## التقارير الأساسية المطلوبة

### 1. تقرير المخزون الحالي (rpt_CurrentInventory)

#### الخطوات:
1. **إنشاء التقرير**:
   - اذهب إلى تبويب `Create`
   - اضغط `Report Wizard`
   - اختر الاستعلام `vw_RawMaterialsDetails`

2. **اختيار الحقول**:
   - اسم المادة
   - الفئة
   - المخزون الحالي
   - الحد الأدنى
   - تكلفة الوحدة
   - قيمة المخزون
   - يحتاج إعادة طلب

3. **التجميع والترتيب**:
   - جمّع حسب الفئة
   - رتب حسب اسم المادة

4. **التخطيط والنمط**:
   - اختر تخطيط `Stepped`
   - اختر اتجاه `Portrait`
   - اسم التقرير: `rpt_CurrentInventory`
   - العنوان: `تقرير المخزون الحالي`

### 2. تقرير المواد منخفضة المخزون (rpt_LowStockMaterials)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `qry_LowStockMaterials`

2. **اختيار الحقول**:
   - المادة الخام
   - المخزون الحالي
   - الحد الأدنى
   - الكمية المطلوبة
   - المورد المفضل

3. **التنسيق**:
   - بدون تجميع
   - رتب حسب الكمية المطلوبة (تنازلي)
   - اسم التقرير: `rpt_LowStockMaterials`
   - العنوان: `تقرير المواد منخفضة المخزون`

### 3. تقرير المبيعات الشهرية (rpt_MonthlySales)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `qry_MonthlySales`

2. **اختيار الحقول**:
   - الشهر
   - عدد الفواتير
   - إجمالي المبيعات
   - إجمالي الضرائب

3. **التنسيق**:
   - بدون تجميع
   - رتب حسب الشهر (تنازلي)
   - أضف مجاميع للأعمدة الرقمية
   - اسم التقرير: `rpt_MonthlySales`
   - العنوان: `تقرير المبيعات الشهرية`

### 4. تقرير أفضل العملاء (rpt_TopCustomers)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `qry_TopCustomers`

2. **اختيار الحقول**:
   - العميل
   - عدد الفواتير
   - إجمالي المبيعات
   - متوسط الفاتورة

3. **التنسيق**:
   - بدون تجميع
   - رتب حسب إجمالي المبيعات (تنازلي)
   - اسم التقرير: `rpt_TopCustomers`
   - العنوان: `تقرير أفضل العملاء`

### 5. تقرير أفضل المنتجات (rpt_TopProducts)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `qry_TopProducts`

2. **اختيار الحقول**:
   - المنتج
   - إجمالي الكمية المباعة
   - إجمالي المبيعات
   - عدد مرات البيع

3. **التنسيق**:
   - بدون تجميع
   - رتب حسب إجمالي المبيعات (تنازلي)
   - اسم التقرير: `rpt_TopProducts`
   - العنوان: `تقرير أفضل المنتجات`

### 6. تقرير تفاصيل الوصفات (rpt_RecipeDetails)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `vw_RecipeDetails`

2. **اختيار الحقول**:
   - اسم الوصفة
   - المادة الخام
   - الكمية المطلوبة
   - الوحدة
   - تكلفة الوحدة
   - تكلفة المادة

3. **التجميع**:
   - جمّع حسب اسم الوصفة
   - أضف مجموع تكلفة المادة لكل وصفة

4. **التنسيق**:
   - اسم التقرير: `rpt_RecipeDetails`
   - العنوان: `تقرير تفاصيل الوصفات`

### 7. تقرير تكلفة الوصفات (rpt_RecipeCosts)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `vw_RecipeTotalCost`

2. **اختيار الحقول**:
   - اسم الوصفة
   - إجمالي التكلفة
   - الكمية المنتجة
   - تكلفة الوحدة

3. **التنسيق**:
   - رتب حسب إجمالي التكلفة (تنازلي)
   - اسم التقرير: `rpt_RecipeCosts`
   - العنوان: `تقرير تكلفة الوصفات`

### 8. تقرير أوامر الإنتاج (rpt_ProductionOrders)

#### الخطوات:
1. **إنشاء التقرير**:
   - استخدم `Report Wizard`
   - اختر الاستعلام `qry_ProductionOrdersDetails`

2. **اختيار الحقول**:
   - رقم أمر الإنتاج
   - المنتج
   - الوصفة
   - الكمية المخططة
   - الكمية الفعلية
   - تاريخ البدء
   - تاريخ الانتهاء
   - الحالة

3. **التنسيق**:
   - جمّع حسب الحالة
   - رتب حسب تاريخ البدء (تنازلي)
   - اسم التقرير: `rpt_ProductionOrders`
   - العنوان: `تقرير أوامر الإنتاج`

## تحسينات التقارير

### 1. إضافة الترويسة والتذييل:

#### ترويسة التقرير:
- شعار الشركة
- اسم الشركة
- عنوان التقرير
- تاريخ الطباعة

#### تذييل التقرير:
- رقم الصفحة
- إجمالي الصفحات
- تاريخ ووقت الطباعة
- اسم المستخدم

### 2. تنسيق البيانات:

#### الأرقام:
- استخدم تنسيق العملة للمبالغ المالية
- استخدم فواصل الآلاف للأرقام الكبيرة
- اضبط عدد المنازل العشرية

#### التواريخ:
- استخدم التنسيق العربي للتواريخ
- أضف أسماء الأشهر بالعربية

#### النصوص:
- استخدم خطوط عربية واضحة
- اضبط اتجاه النص من اليمين لليسار

### 3. إضافة الألوان والتنسيق:

#### الألوان:
- استخدم ألوان متناسقة مع هوية الشركة
- ميز الترويسات بألوان مختلفة
- استخدم ألوان تحذيرية للبيانات المهمة

#### الحدود والخطوط:
- أضف حدود للجداول
- استخدم خطوط فاصلة بين الأقسام
- ميز المجاميع بخطوط سميكة

## إنشاء تقرير مخصص للمخزون المنخفض

### خطوات متقدمة:
1. **إنشاء التقرير**:
   - أنشئ تقرير جديد باستخدام `qry_LowStockMaterials`

2. **إضافة تنسيق شرطي**:
   - ميز المواد التي تحتاج إعادة طلب عاجل بلون أحمر
   - ميز المواد القريبة من النفاد بلون أصفر

3. **إضافة رسم بياني**:
   - أضف رسم بياني يوضح توزيع المواد حسب الفئات
   - أضف رسم بياني للمواد الأكثر احتياجاً لإعادة الطلب

## إنشاء تقرير شامل للمبيعات

### خطوات متقدمة:
1. **دمج عدة استعلامات**:
   - استخدم `qry_MonthlySales` للمبيعات الشهرية
   - استخدم `qry_TopCustomers` لأفضل العملاء
   - استخدم `qry_TopProducts` لأفضل المنتجات

2. **إنشاء تقرير فرعي**:
   - أنشئ تقارير فرعية لكل قسم
   - ادمجها في تقرير رئيسي واحد

3. **إضافة مؤشرات الأداء**:
   - نسبة نمو المبيعات
   - متوسط قيمة الفاتورة
   - عدد العملاء الجدد

## اختبار التقارير

### 1. اختبار البيانات:
- تأكد من صحة البيانات المعروضة
- قارن النتائج مع الاستعلامات الأصلية
- اختبر التقارير مع بيانات مختلفة

### 2. اختبار الطباعة:
- اطبع عينة من كل تقرير
- تأكد من وضوح النصوص والأرقام
- اختبر الطباعة على أحجام ورق مختلفة

### 3. اختبار الأداء:
- قس وقت تحميل التقارير
- اختبر التقارير مع كميات كبيرة من البيانات
- تأكد من عدم تجمد النظام

## نصائح مهمة

1. **استخدم أسماء واضحة** للتقارير
2. **أضف وصف** لكل تقرير يوضح الغرض منه
3. **احفظ نسخ احتياطية** من التقارير المخصصة
4. **اختبر التقارير بانتظام** مع تحديث البيانات
5. **وثق أي تعديلات** تجريها على التقارير

## الخطوة التالية

بعد إكمال التقارير، يمكنك:
1. إنشاء قائمة تقارير في النموذج الرئيسي
2. إضافة أزرار لطباعة التقارير مباشرة
3. إنشاء تقارير مخصصة حسب احتياجات المستخدمين
4. تصدير التقارير إلى PDF أو Excel
