# سكريبت PowerShell لإكمال إنشاء باقي جداول قاعدة البيانات

# تحديد مسار قاعدة البيانات
$dbPath = "C:\Users\<USER>\OneDrive\new\ابوفرح\4445\نظام_إدارة_المصنع.accdb"

try {
    # إنشاء اتصال بقاعدة البيانات
    $connection = New-Object -ComObject ADODB.Connection
    $connection.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=$dbPath")
    
    Write-Host "تم الاتصال بقاعدة البيانات" -ForegroundColor Green
    
    # إنشاء جدول أوامر الإنتاج
    $sql = @"
CREATE TABLE ProductionOrders (
    ProductionOrderID AUTOINCREMENT PRIMARY KEY,
    OrderNumber TEXT(20) NOT NULL,
    ProductID INTEGER NOT NULL,
    RecipeID INTEGER NOT NULL,
    PlannedQuantity DOUBLE NOT NULL,
    ProducedQuantity DOUBLE DEFAULT 0,
    StartDate DATETIME,
    EndDate DATETIME,
    PlannedStartDate DATETIME,
    PlannedEndDate DATETIME,
    Status TEXT(20) DEFAULT 'Planned',
    Priority INTEGER DEFAULT 1,
    TotalCost CURRENCY DEFAULT 0,
    CreatedBy TEXT(50),
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول أوامر الإنتاج"
    
    # إنشاء جدول مراحل الإنتاج
    $sql = @"
CREATE TABLE ProductionStages (
    StageID AUTOINCREMENT PRIMARY KEY,
    StageName TEXT(100) NOT NULL,
    StageOrder INTEGER NOT NULL,
    EstimatedDuration INTEGER,
    Description MEMO,
    IsActive YESNO DEFAULT True
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول مراحل الإنتاج"
    
    # إدراج مراحل الإنتاج الأساسية
    $stages = @(
        @("تحضير المكونات", 1, 30, "تحضير وقياس جميع المكونات المطلوبة"),
        @("خلط العجين", 2, 45, "خلط الدقيق والمكونات الجافة"),
        @("إضافة السوائل", 3, 15, "إضافة الزيت والماء والعسل"),
        @("العجن", 4, 60, "عجن المكونات حتى الحصول على عجينة متماسكة"),
        @("التشكيل", 5, 90, "تشكيل المعمول وحشوه"),
        @("الخبز", 6, 120, "خبز المعمول في الفرن"),
        @("التبريد", 7, 60, "تبريد المنتج النهائي"),
        @("التعبئة", 8, 30, "تعبئة المنتج في الأكياس أو الصناديق")
    )
    
    foreach ($stage in $stages) {
        $sql = "INSERT INTO ProductionStages (StageName, StageOrder, EstimatedDuration, Description) VALUES ('$($stage[0])', $($stage[1]), $($stage[2]), '$($stage[3])')"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج مراحل الإنتاج الأساسية"
    
    # إنشاء جدول مراحل أوامر الإنتاج
    $sql = @"
CREATE TABLE ProductionOrderStages (
    OrderStageID AUTOINCREMENT PRIMARY KEY,
    ProductionOrderID INTEGER NOT NULL,
    StageID INTEGER NOT NULL,
    PlannedStartTime DATETIME,
    PlannedEndTime DATETIME,
    ActualStartTime DATETIME,
    ActualEndTime DATETIME,
    Status TEXT(20) DEFAULT 'Pending',
    ResponsiblePerson TEXT(100),
    Notes MEMO
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول مراحل أوامر الإنتاج"
    
    # إنشاء جدول فواتير المبيعات
    $sql = @"
CREATE TABLE SalesInvoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    InvoiceNumber TEXT(20) NOT NULL,
    CustomerID INTEGER NOT NULL,
    InvoiceDate DATETIME DEFAULT Now(),
    DueDate DATETIME,
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    Status TEXT(20) DEFAULT 'Unpaid',
    SalesmanName TEXT(100),
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول فواتير المبيعات"
    
    # إنشاء جدول تفاصيل فواتير المبيعات
    $sql = @"
CREATE TABLE SalesInvoiceDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    InvoiceID INTEGER NOT NULL,
    ProductID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    DiscountPercent DOUBLE DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetPrice CURRENCY NOT NULL,
    Notes TEXT(255)
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول تفاصيل فواتير المبيعات"
    
    # إنشاء جدول المدفوعات
    $sql = @"
CREATE TABLE Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentType TEXT(20) NOT NULL,
    ReferenceID INTEGER NOT NULL,
    Amount CURRENCY NOT NULL,
    PaymentMethod TEXT(20) NOT NULL,
    CheckNumber TEXT(50),
    BankName TEXT(100),
    Notes TEXT(255),
    CreatedBy TEXT(50),
    CreatedDate DATETIME DEFAULT Now()
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول المدفوعات"
    
    # إنشاء جدول إعدادات النظام
    $sql = @"
CREATE TABLE Settings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingKey TEXT(50) NOT NULL,
    SettingValue TEXT(255),
    Description TEXT(255),
    LastModified DATETIME DEFAULT Now()
)
"@
    $connection.Execute($sql) | Out-Null
    Write-Host "تم إنشاء جدول إعدادات النظام"
    
    # إدراج إعدادات النظام الأساسية
    $settings = @(
        @("CompanyName", "مصنع الحلويات الشرقية", "اسم الشركة"),
        @("CompanyAddress", "الرياض، المملكة العربية السعودية", "عنوان الشركة"),
        @("CompanyPhone", "+966-11-1234567", "هاتف الشركة"),
        @("TaxRate", "15", "معدل الضريبة المضافة"),
        @("Currency", "ريال سعودي", "العملة المستخدمة"),
        @("MinStockAlert", "10", "الحد الأدنى للتنبيه عن المخزون"),
        @("AutoGenerateOrderNumbers", "True", "إنشاء أرقام الطلبات تلقائياً"),
        @("DefaultProductionTime", "480", "وقت الإنتاج الافتراضي بالدقائق")
    )
    
    foreach ($setting in $settings) {
        $sql = "INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES ('$($setting[0])', '$($setting[1])', '$($setting[2])')"
        $connection.Execute($sql) | Out-Null
    }
    Write-Host "تم إدراج إعدادات النظام الأساسية"
    
    # إنشاء فهارس لتحسين الأداء
    Write-Host "إنشاء الفهارس..."
    
    # فهارس للمواد الخام
    $connection.Execute("CREATE INDEX IX_RawMaterials_Code ON RawMaterials (MaterialCode)") | Out-Null
    $connection.Execute("CREATE INDEX IX_RawMaterials_Category ON RawMaterials (CategoryID)") | Out-Null
    
    # فهارس للمنتجات
    $connection.Execute("CREATE INDEX IX_Products_Code ON Products (ProductCode)") | Out-Null
    $connection.Execute("CREATE INDEX IX_Products_Category ON Products (CategoryID)") | Out-Null
    
    # فهارس للعملاء
    $connection.Execute("CREATE INDEX IX_Customers_Code ON Customers (CustomerCode)") | Out-Null
    
    # فهارس لطلبات الشراء
    $connection.Execute("CREATE INDEX IX_PurchaseOrders_Number ON PurchaseOrders (OrderNumber)") | Out-Null
    $connection.Execute("CREATE INDEX IX_PurchaseOrders_Supplier ON PurchaseOrders (SupplierID)") | Out-Null
    $connection.Execute("CREATE INDEX IX_PurchaseOrders_Date ON PurchaseOrders (OrderDate)") | Out-Null
    
    # فهارس لفواتير الشراء
    $connection.Execute("CREATE INDEX IX_PurchaseInvoices_Number ON PurchaseInvoices (InvoiceNumber)") | Out-Null
    $connection.Execute("CREATE INDEX IX_PurchaseInvoices_Supplier ON PurchaseInvoices (SupplierID)") | Out-Null
    $connection.Execute("CREATE INDEX IX_PurchaseInvoices_Date ON PurchaseInvoices (InvoiceDate)") | Out-Null
    
    # فهارس لفواتير المبيعات
    $connection.Execute("CREATE INDEX IX_SalesInvoices_Number ON SalesInvoices (InvoiceNumber)") | Out-Null
    $connection.Execute("CREATE INDEX IX_SalesInvoices_Customer ON SalesInvoices (CustomerID)") | Out-Null
    $connection.Execute("CREATE INDEX IX_SalesInvoices_Date ON SalesInvoices (InvoiceDate)") | Out-Null
    
    # فهارس لأوامر الإنتاج
    $connection.Execute("CREATE INDEX IX_ProductionOrders_Number ON ProductionOrders (OrderNumber)") | Out-Null
    $connection.Execute("CREATE INDEX IX_ProductionOrders_Product ON ProductionOrders (ProductID)") | Out-Null
    $connection.Execute("CREATE INDEX IX_ProductionOrders_Date ON ProductionOrders (PlannedStartDate)") | Out-Null
    
    # فهارس لحركات المخزون
    $connection.Execute("CREATE INDEX IX_InventoryMovements_Date ON InventoryMovements (MovementDate)") | Out-Null
    $connection.Execute("CREATE INDEX IX_InventoryMovements_Item ON InventoryMovements (ItemType, ItemID)") | Out-Null
    
    Write-Host "تم إنشاء الفهارس بنجاح"
    
    # إغلاق الاتصال
    $connection.Close()
    
    Write-Host "تم إكمال إنشاء جميع الجداول والفهارس بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Error "حدث خطأ أثناء إنشاء الجداول: $($_.Exception.Message)"
} finally {
    # تنظيف الكائنات
    if ($connection) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($connection) | Out-Null
    }
}
