# دليل الاختبارات اليدوية المفصلة

## نظرة عامة
هذا الدليل يحتوي على خطوات مفصلة للاختبارات اليدوية التي يجب تنفيذها في Microsoft Access لضمان جودة النظام.

---

## 1. اختبارات الجداول والبيانات (يدوي)

### 1.1 فحص هيكل الجداول

#### الخطوات:
1. **فتح قاعدة البيانات**:
   - افتح `نظام_إدارة_المصنع.accdb`
   - انتقل إلى تبويب `Tables`

2. **فحص الجداول الأساسية**:
   - تأكد من وجود 20 جدول
   - افتح كل جدول وتحقق من:
     - أسماء الحقول باللغة العربية
     - أنواع البيانات صحيحة
     - وجود المفاتيح الأساسية (🔑)

3. **فحص البيانات التجريبية**:
   ```
   ✅ جدول Units: 7 وحدات قياس
   ✅ جدول Categories: 9 فئات
   ✅ جدول Suppliers: 4 موردين
   ✅ جدول RawMaterials: 10 مواد خام
   ✅ جدول Products: 5 منتجات
   ✅ جدول Customers: 4 عملاء
   ✅ جدول Recipes: 2 وصفة
   ✅ جدول ProductionStages: 8 مراحل
   ✅ جدول Settings: 8 إعدادات
   ```

#### معايير النجاح:
- ✅ جميع الجداول موجودة ومرئية
- ✅ البيانات التجريبية مكتملة
- ✅ لا توجد رسائل خطأ عند فتح الجداول

### 1.2 اختبار إدخال البيانات

#### اختبار 1.2.1: إدخال مورد جديد
**الخطوات**:
1. افتح جدول `Suppliers`
2. أضف سجل جديد:
   ```
   SupplierName: شركة اختبار للمواد الغذائية
   Phone: 0112345678
   Email: <EMAIL>
   Address: الرياض، المملكة العربية السعودية
   ```
3. احفظ السجل (Ctrl+S)

**النتيجة المتوقعة**: حفظ السجل بنجاح مع تخصيص SupplierID تلقائياً

#### اختبار 1.2.2: إدخال بيانات غير صحيحة
**الخطوات**:
1. افتح جدول `RawMaterials`
2. حاول إدخال:
   ```
   MaterialName: (اتركه فارغ)
   UnitCost: نص بدلاً من رقم
   ```
3. حاول الحفظ

**النتيجة المتوقعة**: رسائل خطأ واضحة ومنع الحفظ

### 1.3 اختبار التحديث والحذف

#### اختبار 1.3.1: تحديث بيانات مورد
**الخطوات**:
1. افتح جدول `Suppliers`
2. اختر المورد الأول
3. غيّر رقم الهاتف
4. احفظ التغيير

**النتيجة المتوقعة**: حفظ التحديث بنجاح

#### اختبار 1.3.2: محاولة حذف سجل مرتبط
**الخطوات**:
1. افتح جدول `Suppliers`
2. حاول حذف مورد له مواد خام مرتبطة
3. اضغط Delete

**النتيجة المتوقعة**: رسالة خطأ تمنع الحذف

---

## 2. اختبارات العلاقات (يدوي)

### 2.1 فحص العلاقات المرئية

#### الخطوات:
1. **فتح نافذة العلاقات**:
   - اذهب إلى `Database Tools`
   - اضغط `Relationships`

2. **فحص العلاقات**:
   - تأكد من وجود خطوط تربط الجداول
   - تحقق من اتجاه العلاقات (1 إلى ∞)
   - تأكد من وجود رموز المفاتيح

3. **العلاقات المطلوبة**:
   ```
   ✅ Suppliers → RawMaterials
   ✅ Categories → RawMaterials  
   ✅ Units → RawMaterials
   ✅ Categories → Products
   ✅ Units → Products
   ✅ Products → Recipes
   ✅ RawMaterials → RecipeIngredients
   ✅ Recipes → RecipeIngredients
   ✅ Suppliers → PurchaseOrders
   ✅ PurchaseOrders → PurchaseOrderDetails
   ✅ RawMaterials → PurchaseOrderDetails
   ✅ Customers → SalesInvoices
   ✅ SalesInvoices → SalesInvoiceDetails
   ✅ Products → SalesInvoiceDetails
   ✅ Products → ProductionOrders
   ✅ Recipes → ProductionOrders
   ✅ ProductionStages → ProductionOrderStages
   ✅ ProductionOrders → ProductionOrderStages
   ```

#### معايير النجاح:
- ✅ جميع العلاقات مرئية وواضحة
- ✅ لا توجد جداول منعزلة
- ✅ العلاقات منطقية ومتسقة

### 2.2 اختبار سلامة المراجع

#### اختبار 2.2.1: إدخال مفتاح خارجي غير موجود
**الخطوات**:
1. افتح جدول `RawMaterials`
2. أضف مادة خام جديدة
3. في حقل `SupplierID` أدخل رقم غير موجود (مثل 999)
4. حاول الحفظ

**النتيجة المتوقعة**: رسالة خطأ تمنع الحفظ

#### اختبار 2.2.2: حذف سجل مرجع إليه
**الخطوات**:
1. افتح جدول `Categories`
2. حاول حذف فئة لها مواد خام مرتبطة
3. اضغط Delete

**النتيجة المتوقعة**: رسالة خطأ تمنع الحذف

---

## 3. اختبارات الاستعلامات (يدوي)

### 3.1 فحص الاستعلامات الموجودة

#### الخطوات:
1. **انتقل إلى تبويب Queries**
2. **تحقق من وجود الاستعلامات**:
   ```
   ✅ vw_RawMaterialsDetails
   ✅ vw_ProductsDetails
   ✅ vw_RecipeDetails
   ✅ vw_RecipeTotalCost
   ✅ vw_PurchaseOrdersDetails
   ✅ vw_SalesInvoicesDetails
   ✅ qry_LowStockMaterials
   ✅ qry_LowStockProducts
   ✅ qry_MonthlyPurchases
   ✅ qry_MonthlySales
   ✅ qry_TopCustomers
   ✅ qry_TopProducts
   ✅ qry_ProductionOrdersDetails
   ✅ qry_InventoryValue
   ```

3. **اختبار كل استعلام**:
   - انقر نقراً مزدوجاً على كل استعلام
   - تحقق من عرض البيانات بشكل صحيح
   - تأكد من عدم وجود رسائل خطأ

#### معايير النجاح:
- ✅ جميع الاستعلامات تعمل بدون أخطاء
- ✅ البيانات معروضة بتنسيق صحيح
- ✅ الحسابات دقيقة

### 3.2 اختبار دقة الحسابات

#### اختبار 3.2.1: تكلفة الوصفات
**الخطوات**:
1. افتح `vw_RecipeTotalCost`
2. اختر وصفة واحدة
3. احسب التكلفة يدوياً:
   - افتح `vw_RecipeDetails` لنفس الوصفة
   - اجمع (الكمية × تكلفة الوحدة) لجميع المكونات
4. قارن النتيجة مع الاستعلام

**النتيجة المتوقعة**: تطابق الحساب اليدوي مع نتيجة الاستعلام

#### اختبار 3.2.2: قيمة المخزون
**الخطوات**:
1. افتح `qry_InventoryValue`
2. اختر مادة خام واحدة
3. احسب القيمة يدوياً: المخزون الحالي × تكلفة الوحدة
4. قارن مع النتيجة في الاستعلام

**النتيجة المتوقعة**: تطابق الحساب

### 3.3 اختبار الاستعلامات مع بيانات متنوعة

#### اختبار 3.3.1: المواد منخفضة المخزون
**الخطوات**:
1. افتح جدول `RawMaterials`
2. غيّر مخزون إحدى المواد ليصبح أقل من الحد الأدنى
3. افتح `qry_LowStockMaterials`
4. تحقق من ظهور المادة في النتائج

**النتيجة المتوقعة**: ظهور المادة في قائمة المواد منخفضة المخزون

---

## 4. اختبارات السيناريوهات العملية (يدوي)

### 4.1 سيناريو دورة الشراء الكاملة

#### الخطوات:
1. **إنشاء طلب شراء**:
   - افتح جدول `PurchaseOrders`
   - أضف طلب جديد:
     ```
     OrderNumber: PO-MANUAL-001
     SupplierID: 1
     OrderDate: تاريخ اليوم
     Status: Pending
     ```

2. **إضافة تفاصيل الطلب**:
   - افتح جدول `PurchaseOrderDetails`
   - أضف تفصيل:
     ```
     OrderID: (ID الطلب الجديد)
     MaterialID: 1
     Quantity: 100
     UnitPrice: 5.50
     ```

3. **التحقق من الاستعلام**:
   - افتح `vw_PurchaseOrdersDetails`
   - ابحث عن الطلب الجديد
   - تحقق من صحة البيانات المعروضة

#### معايير النجاح:
- ✅ إنشاء الطلب بنجاح
- ✅ ظهور الطلب في الاستعلام
- ✅ صحة جميع البيانات

### 4.2 سيناريو دورة الإنتاج

#### الخطوات:
1. **التحقق من توفر المواد**:
   - افتح `vw_RecipeDetails` للوصفة الأولى
   - تحقق من توفر جميع المواد في المخزون

2. **إنشاء أمر إنتاج**:
   - افتح جدول `ProductionOrders`
   - أضف أمر جديد:
     ```
     OrderNumber: PROD-MANUAL-001
     ProductID: 1
     RecipeID: 1
     PlannedQuantity: 10
     StartDate: تاريخ اليوم
     Status: InProgress
     ```

3. **محاكاة تنفيذ الإنتاج**:
   - احسب المواد المطلوبة (الكمية × 10)
   - قلل المخزون في `RawMaterials`
   - زد المخزون في `Products`
   - حدث حالة الأمر إلى `Completed`

#### معايير النجاح:
- ✅ تحديث المخزون بدقة
- ✅ تسجيل أمر الإنتاج
- ✅ حساب صحيح للمواد المستهلكة

### 4.3 سيناريو دورة المبيعات

#### الخطوات:
1. **إنشاء فاتورة مبيعات**:
   - افتح جدول `SalesInvoices`
   - أضف فاتورة جديدة:
     ```
     InvoiceNumber: SI-MANUAL-001
     CustomerID: 1
     InvoiceDate: تاريخ اليوم
     TaxRate: 15
     ```

2. **إضافة تفاصيل الفاتورة**:
   - افتح جدول `SalesInvoiceDetails`
   - أضف تفصيل:
     ```
     InvoiceID: (ID الفاتورة الجديدة)
     ProductID: 1
     Quantity: 5
     UnitPrice: 25.00
     ```

3. **حساب المجاميع**:
   - SubTotal = 5 × 25.00 = 125.00
   - TaxAmount = 125.00 × 0.15 = 18.75
   - NetAmount = 125.00 + 18.75 = 143.75
   - حدث هذه القيم في جدول `SalesInvoices`

4. **تحديث المخزون**:
   - قلل مخزون المنتج بـ 5 وحدات

#### معايير النجاح:
- ✅ حساب المجاميع بدقة
- ✅ تحديث المخزون
- ✅ ظهور الفاتورة في الاستعلامات

---

## 5. اختبارات الأداء (يدوي)

### 5.1 اختبار سرعة الاستعلامات

#### الخطوات:
1. **قياس وقت التحميل**:
   - افتح كل استعلام وقس الوقت
   - سجل الأوقات:
     ```
     vw_RawMaterialsDetails: ___ ثانية
     vw_RecipeTotalCost: ___ ثانية
     qry_MonthlySales: ___ ثانية
     ```

2. **اختبار مع بيانات إضافية**:
   - أضف 50 مادة خام جديدة
   - أعد قياس أوقات الاستعلامات
   - قارن النتائج

#### معايير النجاح:
- ✅ جميع الاستعلامات تحمل في أقل من 5 ثواني
- ✅ لا يوجد تدهور كبير في الأداء مع البيانات الإضافية

### 5.2 اختبار استهلاك الذاكرة

#### الخطوات:
1. **مراقبة استهلاك الذاكرة**:
   - افتح Task Manager
   - راقب استهلاك Access للذاكرة
   - افتح عدة استعلامات معاً
   - سجل أقصى استهلاك

#### معايير النجاح:
- ✅ استهلاك الذاكرة أقل من 200 ميجابايت
- ✅ لا يوجد تسريب في الذاكرة

---

## 6. اختبارات حالات الخطأ (يدوي)

### 6.1 اختبار مقاومة الأخطاء

#### اختبار 6.1.1: انقطاع الكهرباء المحاكى
**الخطوات**:
1. ابدأ إدخال بيانات كبيرة
2. أغلق Access فجأة (Alt+F4)
3. أعد فتح قاعدة البيانات
4. تحقق من سلامة البيانات

**النتيجة المتوقعة**: عدم فساد قاعدة البيانات

#### اختبار 6.1.2: إدخال بيانات متطرفة
**الخطوات**:
1. أدخل نصوص طويلة جداً
2. أدخل أرقام كبيرة جداً
3. أدخل تواريخ غير منطقية
4. راقب سلوك النظام

**النتيجة المتوقعة**: رسائل خطأ واضحة أو قبول البيانات بأمان

---

## 7. تقرير الاختبارات اليدوية

### نموذج تقرير:

```
=== تقرير الاختبارات اليدوية ===
التاريخ: ___________
المختبر: ___________

1. اختبارات الجداول:
   ✅/❌ فحص هيكل الجداول
   ✅/❌ إدخال البيانات
   ✅/❌ التحديث والحذف
   
2. اختبارات العلاقات:
   ✅/❌ فحص العلاقات المرئية
   ✅/❌ سلامة المراجع
   
3. اختبارات الاستعلامات:
   ✅/❌ تشغيل الاستعلامات
   ✅/❌ دقة الحسابات
   
4. السيناريوهات العملية:
   ✅/❌ دورة الشراء
   ✅/❌ دورة الإنتاج
   ✅/❌ دورة المبيعات
   
5. اختبارات الأداء:
   ✅/❌ سرعة الاستعلامات
   ✅/❌ استهلاك الذاكرة
   
6. حالات الخطأ:
   ✅/❌ مقاومة الأخطاء
   ✅/❌ البيانات المتطرفة

الملاحظات:
_________________________________
_________________________________

التوصيات:
_________________________________
_________________________________

الموافقة على النظام: ✅/❌
```

---

## الخطوات التالية

بعد إكمال الاختبارات اليدوية:
1. **املأ تقرير الاختبارات** بالنتائج
2. **وثق أي مشاكل** مكتشفة
3. **قارن النتائج** مع الاختبارات التلقائية
4. **أعد الاختبارات** بعد إصلاح المشاكل
5. **احصل على الموافقة** النهائية على النظام
