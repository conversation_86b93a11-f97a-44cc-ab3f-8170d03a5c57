-- نظام إدارة مصنع المواد الغذائية
-- تصميم قاعدة البيانات الأساسية

-- جدول وحدات القياس
CREATE TABLE Units (
    UnitID AUTOINCREMENT PRIMARY KEY,
    UnitName NVARCHAR(50) NOT NULL,
    UnitNameArabic NVARCHAR(50) NOT NULL,
    Description NVARCHAR(255)
);

-- جدول فئات المنتجات والمواد
CREATE TABLE Categories (
    CategoryID AUTOINCREMENT PRIMARY KEY,
    CategoryName NVARCHAR(100) NOT NULL,
    CategoryType NVARCHAR(20) NOT NULL, -- 'RawMaterial' أو 'Product'
    Description NVARCHAR(255)
);

-- جدول الموردين
CREATE TABLE Suppliers (
    SupplierID AUTOINCREMENT PRIMARY KEY,
    SupplierName NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100),
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(255),
    City NVARCHAR(50),
    Country NVARCHAR(50),
    TaxNumber NVARCHAR(50),
    Rating INTEGER DEFAULT 0, -- تقييم من 1 إلى 5
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT NOW(),
    Notes MEMO
);

-- جدول المواد الخام
CREATE TABLE RawMaterials (
    MaterialID AUTOINCREMENT PRIMARY KEY,
    MaterialCode NVARCHAR(20) UNIQUE NOT NULL,
    MaterialName NVARCHAR(100) NOT NULL,
    CategoryID INTEGER,
    UnitID INTEGER NOT NULL,
    MinStockLevel DOUBLE DEFAULT 0,
    MaxStockLevel DOUBLE DEFAULT 0,
    CurrentStock DOUBLE DEFAULT 0,
    UnitCost CURRENCY DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT NOW(),
    Description MEMO,
    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول المنتجات النهائية
CREATE TABLE Products (
    ProductID AUTOINCREMENT PRIMARY KEY,
    ProductCode NVARCHAR(20) UNIQUE NOT NULL,
    ProductName NVARCHAR(100) NOT NULL,
    CategoryID INTEGER,
    UnitID INTEGER NOT NULL,
    MinStockLevel DOUBLE DEFAULT 0,
    MaxStockLevel DOUBLE DEFAULT 0,
    CurrentStock DOUBLE DEFAULT 0,
    SalePrice CURRENCY DEFAULT 0,
    ProductionCost CURRENCY DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT NOW(),
    Description MEMO,
    FOREIGN KEY (CategoryID) REFERENCES Categories(CategoryID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول الوصفات
CREATE TABLE Recipes (
    RecipeID AUTOINCREMENT PRIMARY KEY,
    ProductID INTEGER NOT NULL,
    RecipeName NVARCHAR(100) NOT NULL,
    BatchSize DOUBLE NOT NULL, -- حجم الدفعة الواحدة
    PreparationTime INTEGER, -- وقت التحضير بالدقائق
    CookingTime INTEGER, -- وقت الطبخ بالدقائق
    TotalTime INTEGER, -- إجمالي الوقت
    Instructions MEMO,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- جدول مكونات الوصفات
CREATE TABLE RecipeIngredients (
    IngredientID AUTOINCREMENT PRIMARY KEY,
    RecipeID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitID INTEGER NOT NULL,
    Notes NVARCHAR(255),
    FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID)
);

-- جدول طلبات الشراء
CREATE TABLE PurchaseOrders (
    PurchaseOrderID AUTOINCREMENT PRIMARY KEY,
    OrderNumber NVARCHAR(20) UNIQUE NOT NULL,
    SupplierID INTEGER NOT NULL,
    OrderDate DATETIME DEFAULT NOW(),
    RequiredDate DATETIME,
    Status NVARCHAR(20) DEFAULT 'Pending', -- 'Pending', 'Approved', 'Sent', 'Received', 'Cancelled'
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT NOW(),
    ApprovedBy NVARCHAR(50),
    ApprovedDate DATETIME,
    Notes MEMO,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID)
);

-- جدول تفاصيل طلبات الشراء
CREATE TABLE PurchaseOrderDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    PurchaseOrderID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    ReceivedQuantity DOUBLE DEFAULT 0,
    Notes NVARCHAR(255),
    FOREIGN KEY (PurchaseOrderID) REFERENCES PurchaseOrders(PurchaseOrderID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID)
);

-- جدول فواتير الشراء
CREATE TABLE PurchaseInvoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    InvoiceNumber NVARCHAR(20) UNIQUE NOT NULL,
    SupplierID INTEGER NOT NULL,
    PurchaseOrderID INTEGER,
    InvoiceDate DATETIME DEFAULT NOW(),
    DueDate DATETIME,
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Unpaid', -- 'Unpaid', 'Partial', 'Paid'
    CreatedDate DATETIME DEFAULT NOW(),
    Notes MEMO,
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (PurchaseOrderID) REFERENCES PurchaseOrders(PurchaseOrderID)
);

-- جدول تفاصيل فواتير الشراء
CREATE TABLE PurchaseInvoiceDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    InvoiceID INTEGER NOT NULL,
    MaterialID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    ExpiryDate DATETIME,
    BatchNumber NVARCHAR(50),
    Notes NVARCHAR(255),
    FOREIGN KEY (InvoiceID) REFERENCES PurchaseInvoices(InvoiceID),
    FOREIGN KEY (MaterialID) REFERENCES RawMaterials(MaterialID)
);

-- جدول حركات المخزون
CREATE TABLE InventoryMovements (
    MovementID AUTOINCREMENT PRIMARY KEY,
    MovementDate DATETIME DEFAULT NOW(),
    MovementType NVARCHAR(20) NOT NULL, -- 'In', 'Out', 'Transfer', 'Adjustment'
    ItemType NVARCHAR(20) NOT NULL, -- 'RawMaterial', 'Product'
    ItemID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitCost CURRENCY DEFAULT 0,
    TotalCost CURRENCY DEFAULT 0,
    ReferenceType NVARCHAR(20), -- 'Purchase', 'Sale', 'Production', 'Adjustment'
    ReferenceID INTEGER,
    Notes NVARCHAR(255),
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT NOW()
);

-- جدول العملاء
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerCode NVARCHAR(20) UNIQUE NOT NULL,
    CustomerName NVARCHAR(100) NOT NULL,
    ContactPerson NVARCHAR(100),
    Phone NVARCHAR(20),
    Mobile NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(255),
    City NVARCHAR(50),
    Country NVARCHAR(50),
    TaxNumber NVARCHAR(50),
    CreditLimit CURRENCY DEFAULT 0,
    CurrentBalance CURRENCY DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedDate DATETIME DEFAULT NOW(),
    Notes MEMO
);

-- جدول أوامر الإنتاج
CREATE TABLE ProductionOrders (
    ProductionOrderID AUTOINCREMENT PRIMARY KEY,
    OrderNumber NVARCHAR(20) UNIQUE NOT NULL,
    ProductID INTEGER NOT NULL,
    RecipeID INTEGER NOT NULL,
    PlannedQuantity DOUBLE NOT NULL,
    ProducedQuantity DOUBLE DEFAULT 0,
    StartDate DATETIME,
    EndDate DATETIME,
    PlannedStartDate DATETIME,
    PlannedEndDate DATETIME,
    Status NVARCHAR(20) DEFAULT 'Planned', -- 'Planned', 'InProgress', 'Completed', 'Cancelled'
    Priority INTEGER DEFAULT 1, -- 1=عادي، 2=مهم، 3=عاجل
    TotalCost CURRENCY DEFAULT 0,
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT NOW(),
    Notes MEMO,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID),
    FOREIGN KEY (RecipeID) REFERENCES Recipes(RecipeID)
);

-- جدول مراحل الإنتاج
CREATE TABLE ProductionStages (
    StageID AUTOINCREMENT PRIMARY KEY,
    StageName NVARCHAR(100) NOT NULL,
    StageOrder INTEGER NOT NULL,
    EstimatedDuration INTEGER, -- بالدقائق
    Description MEMO,
    IsActive BIT DEFAULT 1
);

-- جدول مراحل أوامر الإنتاج
CREATE TABLE ProductionOrderStages (
    OrderStageID AUTOINCREMENT PRIMARY KEY,
    ProductionOrderID INTEGER NOT NULL,
    StageID INTEGER NOT NULL,
    PlannedStartTime DATETIME,
    PlannedEndTime DATETIME,
    ActualStartTime DATETIME,
    ActualEndTime DATETIME,
    Status NVARCHAR(20) DEFAULT 'Pending', -- 'Pending', 'InProgress', 'Completed', 'Skipped'
    ResponsiblePerson NVARCHAR(100),
    Notes MEMO,
    FOREIGN KEY (ProductionOrderID) REFERENCES ProductionOrders(ProductionOrderID),
    FOREIGN KEY (StageID) REFERENCES ProductionStages(StageID)
);

-- جدول فواتير المبيعات
CREATE TABLE SalesInvoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    InvoiceNumber NVARCHAR(20) UNIQUE NOT NULL,
    CustomerID INTEGER NOT NULL,
    InvoiceDate DATETIME DEFAULT NOW(),
    DueDate DATETIME,
    TotalAmount CURRENCY DEFAULT 0,
    TaxAmount CURRENCY DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    Status NVARCHAR(20) DEFAULT 'Unpaid', -- 'Unpaid', 'Partial', 'Paid'
    SalesmanName NVARCHAR(100),
    CreatedDate DATETIME DEFAULT NOW(),
    Notes MEMO,
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID)
);

-- جدول تفاصيل فواتير المبيعات
CREATE TABLE SalesInvoiceDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    InvoiceID INTEGER NOT NULL,
    ProductID INTEGER NOT NULL,
    Quantity DOUBLE NOT NULL,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    DiscountPercent DOUBLE DEFAULT 0,
    DiscountAmount CURRENCY DEFAULT 0,
    NetPrice CURRENCY NOT NULL,
    Notes NVARCHAR(255),
    FOREIGN KEY (InvoiceID) REFERENCES SalesInvoices(InvoiceID),
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID)
);

-- جدول المدفوعات
CREATE TABLE Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    PaymentDate DATETIME DEFAULT NOW(),
    PaymentType NVARCHAR(20) NOT NULL, -- 'Purchase', 'Sale'
    ReferenceID INTEGER NOT NULL, -- InvoiceID
    Amount CURRENCY NOT NULL,
    PaymentMethod NVARCHAR(20) NOT NULL, -- 'Cash', 'Check', 'Transfer', 'Card'
    CheckNumber NVARCHAR(50),
    BankName NVARCHAR(100),
    Notes NVARCHAR(255),
    CreatedBy NVARCHAR(50),
    CreatedDate DATETIME DEFAULT NOW()
);

-- جدول إعدادات النظام
CREATE TABLE Settings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingKey NVARCHAR(50) UNIQUE NOT NULL,
    SettingValue NVARCHAR(255),
    Description NVARCHAR(255),
    LastModified DATETIME DEFAULT NOW()
);
