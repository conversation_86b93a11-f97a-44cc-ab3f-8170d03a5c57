# خطة اختبار نظام إدارة المصنع

## نظرة عامة
هذه الخطة تحدد الاختبارات المطلوبة للتأكد من صحة عمل جميع وظائف نظام إدارة مصنع المواد الغذائية.

## أهداف الاختبار
- التأكد من صحة إنشاء قاعدة البيانات
- اختبار جميع النماذج والتقارير
- التحقق من صحة العمليات الحسابية
- اختبار التكامل بين الوحدات المختلفة
- التأكد من صحة تحديث المخزون

## مراحل الاختبار

### المرحلة 1: اختبار قاعدة البيانات الأساسية

#### 1.1 اختبار إنشاء الجداول
```sql
-- التحقق من وجود جميع الجداول
SELECT COUNT(*) FROM MSysObjects WHERE Type = 1 AND Name NOT LIKE 'MSys*';
-- يجب أن يكون العدد 22 جدول

-- التحقق من البيانات الأساسية
SELECT COUNT(*) FROM Units; -- يجب أن يكون 7
SELECT COUNT(*) FROM Categories; -- يجب أن يكون 9
SELECT COUNT(*) FROM ProductionStages; -- يجب أن يكون 8
```

#### 1.2 اختبار العلاقات بين الجداول
- التحقق من المفاتيح الخارجية
- اختبار الربط بين الجداول الرئيسية والفرعية
- التأكد من سلامة البيانات المرجعية

#### 1.3 اختبار الفهارس
- التحقق من إنشاء جميع الفهارس
- قياس أداء الاستعلامات

### المرحلة 2: اختبار البيانات التجريبية

#### 2.1 التحقق من البيانات المدرجة
```sql
-- التحقق من الموردين
SELECT COUNT(*) FROM Suppliers; -- يجب أن يكون 4

-- التحقق من المواد الخام
SELECT COUNT(*) FROM RawMaterials; -- يجب أن يكون 10

-- التحقق من المنتجات
SELECT COUNT(*) FROM Products; -- يجب أن يكون 5

-- التحقق من الوصفات
SELECT COUNT(*) FROM Recipes; -- يجب أن يكون 2
SELECT COUNT(*) FROM RecipeIngredients; -- يجب أن يكون 12

-- التحقق من العملاء
SELECT COUNT(*) FROM Customers; -- يجب أن يكون 4
```

#### 2.2 اختبار صحة البيانات
- التأكد من عدم وجود قيم فارغة في الحقول المطلوبة
- التحقق من صحة أنواع البيانات
- اختبار القيود والتحقق من صحة البيانات

### المرحلة 3: اختبار الاستعلامات

#### 3.1 اختبار الاستعلامات الأساسية
```sql
-- اختبار استعلام تفاصيل المواد الخام
SELECT COUNT(*) FROM vw_RawMaterialsDetails;

-- اختبار استعلام تفاصيل المنتجات
SELECT COUNT(*) FROM vw_ProductsDetails;

-- اختبار استعلام تكلفة الوصفات
SELECT * FROM vw_RecipeTotalCost;
```

#### 3.2 اختبار الاستعلامات المحسوبة
- حساب تكلفة الوصفات
- حساب مستويات المخزون
- حساب الربحية

#### 3.3 اختبار استعلامات التقارير
- استعلامات المخزون
- استعلامات المبيعات
- استعلامات الإنتاج

### المرحلة 4: اختبار العمليات التجارية

#### 4.1 اختبار دورة المشتريات
1. **إنشاء طلب شراء جديد**:
   - اختيار مورد
   - إضافة مواد خام
   - حساب المجاميع
   - حفظ الطلب

2. **تحويل طلب الشراء إلى فاتورة**:
   - إنشاء فاتورة شراء
   - ربطها بطلب الشراء
   - تحديث المخزون

3. **التحقق من تحديث المخزون**:
   ```sql
   -- قبل الشراء
   SELECT CurrentStock FROM RawMaterials WHERE MaterialID = 1;
   
   -- بعد الشراء (يجب أن يزيد المخزون)
   SELECT CurrentStock FROM RawMaterials WHERE MaterialID = 1;
   ```

#### 4.2 اختبار دورة الإنتاج
1. **إنشاء أمر إنتاج**:
   - اختيار منتج ووصفة
   - تحديد الكمية المطلوبة
   - التحقق من توفر المواد الخام

2. **تنفيذ أمر الإنتاج**:
   - صرف المواد الخام من المخزون
   - إضافة المنتج النهائي للمخزون
   - تسجيل حركات المخزون

3. **التحقق من الحسابات**:
   ```sql
   -- التحقق من صرف المواد الخام
   SELECT * FROM InventoryMovements 
   WHERE ReferenceType = 'Production' AND MovementType = 'Out';
   
   -- التحقق من إضافة المنتج النهائي
   SELECT * FROM InventoryMovements 
   WHERE ReferenceType = 'Production' AND MovementType = 'In';
   ```

#### 4.3 اختبار دورة المبيعات
1. **إنشاء فاتورة مبيعات**:
   - اختيار عميل
   - إضافة منتجات
   - حساب الأسعار والضرائب

2. **تحديث المخزون**:
   - صرف المنتجات من المخزون
   - تسجيل حركة المخزون

3. **تسجيل الدفع**:
   - إضافة دفعة جديدة
   - تحديث حالة الفاتورة

### المرحلة 5: اختبار الوظائف المتقدمة (VBA)

#### 5.1 اختبار دالة حساب المواد المطلوبة
```vba
' اختبار الدالة مع أمر إنتاج موجود
Debug.Print CalculateRequiredMaterials(1)
```

#### 5.2 اختبار دالة تحديث المخزون للإنتاج
```vba
' اختبار تحديث المخزون
Debug.Print UpdateInventoryForProduction(1)
```

#### 5.3 اختبار دالة إنشاء أرقام الطلبات
```vba
' اختبار إنشاء رقم طلب شراء
Debug.Print GenerateOrderNumber("Purchase")
```

#### 5.4 اختبار دالة فحص المخزون المنخفض
```vba
' اختبار فحص المخزون
Debug.Print CheckLowStockLevels()
```

### المرحلة 6: اختبار التقارير

#### 6.1 اختبار تقارير المخزون
- تقرير المخزون الحالي
- تقرير المواد منخفضة المخزون
- تقرير حركات المخزون

#### 6.2 اختبار تقارير المبيعات
- تقرير المبيعات الشهرية
- تقرير أفضل العملاء
- تقرير أفضل المنتجات

#### 6.3 اختبار التقارير المالية
- تقرير الربحية
- تقرير المستحقات
- تقرير أداء الموردين

### المرحلة 7: اختبار الأداء

#### 7.1 اختبار سرعة الاستعلامات
- قياس وقت تنفيذ الاستعلامات الرئيسية
- اختبار الأداء مع كميات كبيرة من البيانات

#### 7.2 اختبار استهلاك الذاكرة
- مراقبة استهلاك الذاكرة أثناء التشغيل
- اختبار فتح عدة نماذج في نفس الوقت

### المرحلة 8: اختبار واجهة المستخدم

#### 8.1 اختبار النماذج
- سهولة الاستخدام
- وضوح التسميات العربية
- صحة التنقل بين النماذج

#### 8.2 اختبار التحقق من البيانات
- رسائل الخطأ المناسبة
- التحقق من صحة البيانات المدخلة
- منع إدخال بيانات غير صحيحة

## سيناريوهات الاختبار التفصيلية

### سيناريو 1: دورة عمل كاملة لإنتاج معمول الجوز
1. شراء المواد الخام اللازمة
2. إنشاء أمر إنتاج لـ 100 قطعة معمول جوز
3. تنفيذ الإنتاج وتحديث المخزون
4. بيع 50 قطعة لعميل
5. إنشاء التقارير المطلوبة

### سيناريو 2: إدارة المخزون المنخفض
1. تقليل مخزون مادة خام إلى ما دون الحد الأدنى
2. التحقق من ظهور التنبيه
3. إنشاء طلب شراء لإعادة التخزين
4. استلام المواد وتحديث المخزون

### سيناريو 3: تحليل الربحية
1. إدخال عدة فواتير مبيعات
2. حساب تكلفة الإنتاج
3. إنشاء تقرير الربحية
4. تحليل أداء المنتجات المختلفة

## معايير النجاح

### معايير الوظائف الأساسية:
- ✅ جميع الجداول تم إنشاؤها بنجاح
- ✅ البيانات التجريبية تم إدراجها بالكامل
- ✅ جميع الاستعلامات تعمل بشكل صحيح
- ✅ العمليات الحسابية دقيقة 100%

### معايير الأداء:
- ⏱️ الاستعلامات تنفذ في أقل من 2 ثانية
- 💾 استهلاك الذاكرة أقل من 100 ميجابايت
- 🔄 النظام يعمل بدون أخطاء لمدة 8 ساعات متواصلة

### معايير واجهة المستخدم:
- 🎨 جميع النصوص باللغة العربية
- 📱 واجهة سهلة الاستخدام ومفهومة
- ⚠️ رسائل خطأ واضحة ومفيدة

## تقرير الاختبار النهائي

بعد إكمال جميع مراحل الاختبار، يجب إعداد تقرير شامل يتضمن:
- نتائج كل مرحلة اختبار
- المشاكل المكتشفة وحلولها
- توصيات للتحسين
- تأكيد جاهزية النظام للاستخدام

## الخطوات التالية

بعد نجاح الاختبارات:
1. إعداد دليل المستخدم النهائي
2. تدريب المستخدمين
3. نشر النظام في بيئة الإنتاج
4. وضع خطة الصيانة والدعم
