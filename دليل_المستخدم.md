# دليل المستخدم - نظام إدارة مصنع المواد الغذائية

## مقدمة
نظام إدارة مصنع المواد الغذائية هو نظام شامل مصمم خصيصاً لإدارة عمليات مصانع الحلويات والمواد الغذائية، مع التركيز على إنتاج المعمول بالجوز والعسل.

## متطلبات النظام
- Microsoft Access 2016 أو أحدث
- نظام التشغيل Windows 10 أو أحدث
- ذاكرة وصول عشوائي 4 جيجابايت على الأقل
- مساحة تخزين 500 ميجابايت

## بدء التشغيل
1. افتح ملف `نظام_إدارة_المصنع.accdb`
2. اضغط على "تمكين المحتوى" إذا ظهرت رسالة الأمان
3. ستظهر الواجهة الرئيسية للنظام

## الواجهة الرئيسية
تحتوي الواجهة الرئيسية على الوحدات التالية:

### 1. إدارة الموردين
- **إضافة مورد جديد**: إدخال معلومات المورد الكاملة
- **تعديل بيانات المورد**: تحديث معلومات الاتصال والتقييم
- **البحث عن المورد**: البحث بالاسم أو رقم الهاتف
- **تقييم المورد**: تقييم من 1 إلى 5 نجوم

### 2. إدارة المواد الخام
- **إضافة مادة خام جديدة**: تحديد الكود والاسم والفئة
- **تحديد مستويات المخزون**: الحد الأدنى والأقصى
- **تتبع التكلفة**: تكلفة الوحدة والقيمة الإجمالية
- **تنبيهات المخزون**: تنبيهات عند انخفاض المخزون

### 3. إدارة المنتجات النهائية
- **إضافة منتج جديد**: تحديد المواصفات والأسعار
- **حساب الربحية**: الفرق بين سعر البيع وتكلفة الإنتاج
- **إدارة المخزون**: تتبع الكميات المتاحة
- **تصنيف المنتجات**: تنظيم المنتجات حسب الفئات

### 4. إدارة الوصفات
- **إنشاء وصفة جديدة**: تحديد المكونات والكميات
- **حساب التكلفة**: تكلفة الوصفة الإجمالية وتكلفة الوحدة
- **تعليمات الإنتاج**: خطوات التحضير والطبخ
- **حجم الدفعة**: تحديد كمية الإنتاج لكل دفعة

### 5. طلبات الشراء
- **إنشاء طلب شراء**: اختيار المورد والمواد المطلوبة
- **متابعة الطلبات**: تتبع حالة الطلب (معلق، مرسل، مستلم)
- **حساب التكاليف**: المجموع والضرائب والخصومات
- **طباعة الطلب**: إنشاء طلب شراء للإرسال للمورد

### 6. فواتير الشراء
- **تسجيل الفاتورة**: إدخال تفاصيل الفاتورة المستلمة
- **ربط بطلب الشراء**: ربط الفاتورة بطلب الشراء المقابل
- **تحديث المخزون**: إضافة المواد المستلمة للمخزون تلقائياً
- **متابعة المدفوعات**: تسجيل المبالغ المدفوعة والمستحقة

### 7. أوامر الإنتاج
- **إنشاء أمر إنتاج**: اختيار المنتج والكمية المطلوبة
- **اختيار الوصفة**: تحديد الوصفة المناسبة للإنتاج
- **حساب المواد المطلوبة**: حساب تلقائي للمواد الخام المطلوبة
- **تتبع مراحل الإنتاج**: متابعة تقدم العمل في كل مرحلة
- **تحديث المخزون**: صرف المواد الخام وإضافة المنتج النهائي

### 8. إدارة العملاء
- **إضافة عميل جديد**: معلومات الاتصال والحد الائتماني
- **تتبع الرصيد**: المبالغ المستحقة والمدفوعة
- **تاريخ التعامل**: آخر عمليات الشراء والدفع
- **تقييم العميل**: تصنيف العملاء حسب حجم التعامل

### 9. فواتير المبيعات
- **إنشاء فاتورة مبيعات**: اختيار العميل والمنتجات
- **حساب الأسعار**: الأسعار والخصومات والضرائب
- **تحديث المخزون**: صرف المنتجات من المخزون تلقائياً
- **طباعة الفاتورة**: إنشاء فاتورة للعميل

### 10. التقارير
#### تقارير المخزون:
- **تقرير المخزون الحالي**: جميع المواد والمنتجات
- **تقرير المواد منخفضة المخزون**: المواد التي تحتاج إعادة طلب
- **تقرير حركات المخزون**: جميع عمليات الإدخال والإخراج

#### تقارير المبيعات:
- **تقرير المبيعات الشهرية**: إجمالي المبيعات لكل شهر
- **تقرير أفضل العملاء**: العملاء الأكثر شراءً
- **تقرير أفضل المنتجات**: المنتجات الأكثر مبيعاً

#### تقارير الإنتاج:
- **تقرير أوامر الإنتاج**: حالة جميع أوامر الإنتاج
- **تقرير الإنتاجية**: كمية الإنتاج اليومية والشهرية
- **تقرير تكلفة الإنتاج**: تكلفة إنتاج كل منتج

#### التقارير المالية:
- **تقرير الربحية**: الأرباح الشهرية والسنوية
- **تقرير المستحقات**: الفواتير المستحقة للعملاء والموردين
- **تقرير التدفق النقدي**: المقبوضات والمدفوعات

## نصائح للاستخدام الأمثل

### 1. إدخال البيانات الأساسية أولاً:
- أدخل جميع الموردين قبل إنشاء طلبات الشراء
- أدخل جميع المواد الخام قبل إنشاء الوصفات
- أدخل جميع المنتجات والوصفات قبل إنشاء أوامر الإنتاج

### 2. تحديث المخزون بانتظام:
- تأكد من تسجيل جميع فواتير الشراء
- تأكد من تسجيل جميع أوامر الإنتاج
- راجع تقرير المخزون أسبوعياً

### 3. متابعة المستحقات:
- راجع تقرير المستحقات شهرياً
- تابع مع العملاء المتأخرين في السداد
- تابع مع الموردين لضمان التسليم في الوقت المحدد

### 4. تحليل الأداء:
- راجع تقارير الربحية شهرياً
- حلل أداء المنتجات والعملاء
- استخدم البيانات لاتخاذ قرارات تجارية مدروسة

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها:

**1. رسالة "قاعدة البيانات مقفلة":**
- تأكد من عدم فتح النظام على أجهزة أخرى
- أعد تشغيل Access
- تحقق من صلاحيات الملف

**2. بطء في الأداء:**
- أغلق التقارير والنماذج غير المستخدمة
- قم بضغط قاعدة البيانات من قائمة أدوات قاعدة البيانات
- تأكد من وجود مساحة كافية على القرص الصلب

**3. خطأ في الحسابات:**
- تحقق من صحة البيانات المدخلة
- تأكد من تحديث أسعار المواد الخام
- راجع الوصفات والكميات المحددة

**4. مشاكل في الطباعة:**
- تحقق من إعدادات الطابعة
- تأكد من تثبيت برنامج PDF reader
- جرب طباعة تقرير آخر للتأكد من المشكلة

## النسخ الاحتياطي
- انسخ ملف قاعدة البيانات يومياً
- احتفظ بنسخ احتياطية في مواقع مختلفة
- اختبر النسخ الاحتياطية بانتظام

## الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- تحقق من الأسئلة الشائعة
- اتصل بفريق الدعم الفني

---
**ملاحظة**: هذا النظام مصمم خصيصاً لمصانع المواد الغذائية ويمكن تخصيصه حسب احتياجات كل مصنع.
