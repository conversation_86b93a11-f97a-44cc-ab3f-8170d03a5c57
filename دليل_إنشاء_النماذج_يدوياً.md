# دليل إنشاء النماذج يدوياً في Microsoft Access

## الخطوات العامة لإنشاء النماذج

### 1. فتح قاعدة البيانات
- افتح ملف `نظام_إدارة_المصنع.accdb`
- اضغط "Enable Content" إذا ظهر تحذير الأمان

### 2. التحقق من وجود الجداول والاستعلامات
يجب أن ترى الآن:
- **Tables**: 20 جدول
- **Queries**: 14 استعلام
- **Relationships**: العلاقات بين الجداول

## إنشاء النماذج الأساسية

### النموذج 1: إدارة الموردين (frm_Suppliers)

#### الخطوات:
1. **إنشاء النموذج**:
   - اذهب <PERSON>لى تبويب `Create`
   - اضغط `Form Wizard`
   - اختر جدول `Suppliers`
   - اختر جميع الحقول أو الحقول التالية:
     - SupplierName (اسم المورد)
     - ContactPerson (الشخص المسؤول)
     - Phone (الهاتف)
     - Email (البريد الإلكتروني)
     - Address (العنوان)
     - TaxNumber (الرقم الضريبي)
     - Rating (التقييم)
     - IsActive (نشط)

2. **تخصيص النموذج**:
   - اختر تخطيط `Columnar`
   - اختر نمط مناسب
   - اسم النموذج: `frm_Suppliers`
   - العنوان: `إدارة الموردين`

3. **تحسين التصميم**:
   - اضغط `Design View`
   - غير تسميات الحقول للعربية:
     - SupplierName → اسم المورد
     - ContactPerson → الشخص المسؤول
     - Phone → الهاتف
     - Email → البريد الإلكتروني
     - Address → العنوان
     - TaxNumber → الرقم الضريبي
     - Rating → التقييم
     - IsActive → نشط

4. **إضافة أزرار التنقل**:
   - أضف أزرار: جديد، حفظ، حذف، إغلاق
   - استخدم `Button Wizard` لإنشاء الأزرار

### النموذج 2: إدارة المواد الخام (frm_RawMaterials)

#### الخطوات:
1. **إنشاء النموذج**:
   - استخدم `Form Wizard`
   - اختر جدول `RawMaterials`
   - الحقول المطلوبة:
     - MaterialName (اسم المادة)
     - CategoryID (الفئة)
     - UnitID (الوحدة)
     - SupplierID (المورد)
     - CurrentStock (المخزون الحالي)
     - MinStockLevel (الحد الأدنى)
     - UnitCost (تكلفة الوحدة)
     - IsActive (نشط)

2. **تحسين القوائم المنسدلة**:
   - CategoryID: اربطها بجدول Categories
   - UnitID: اربطها بجدول Units
   - SupplierID: اربطها بجدول Suppliers

3. **التسميات العربية**:
   - MaterialName → اسم المادة الخام
   - CategoryID → الفئة
   - UnitID → وحدة القياس
   - SupplierID → المورد المفضل
   - CurrentStock → المخزون الحالي
   - MinStockLevel → الحد الأدنى للمخزون
   - UnitCost → تكلفة الوحدة
   - IsActive → نشط

### النموذج 3: إدارة المنتجات (frm_Products)

#### الخطوات:
1. **إنشاء النموذج**:
   - استخدم `Form Wizard`
   - اختر جدول `Products`
   - الحقول المطلوبة:
     - ProductName (اسم المنتج)
     - CategoryID (الفئة)
     - UnitID (الوحدة)
     - CurrentStock (المخزون الحالي)
     - MinStockLevel (الحد الأدنى)
     - SalePrice (سعر البيع)
     - IsActive (نشط)

2. **التسميات العربية**:
   - ProductName → اسم المنتج
   - CategoryID → الفئة
   - UnitID → وحدة القياس
   - CurrentStock → المخزون الحالي
   - MinStockLevel → الحد الأدنى للمخزون
   - SalePrice → سعر البيع
   - IsActive → نشط

### النموذج 4: إدارة العملاء (frm_Customers)

#### الخطوات:
1. **إنشاء النموذج**:
   - استخدم `Form Wizard`
   - اختر جدول `Customers`
   - الحقول المطلوبة:
     - CustomerName (اسم العميل)
     - ContactPerson (الشخص المسؤول)
     - Phone (الهاتف)
     - Email (البريد الإلكتروني)
     - Address (العنوان)
     - TaxNumber (الرقم الضريبي)
     - CreditLimit (حد الائتمان)
     - IsActive (نشط)

2. **التسميات العربية**:
   - CustomerName → اسم العميل
   - ContactPerson → الشخص المسؤول
   - Phone → الهاتف
   - Email → البريد الإلكتروني
   - Address → العنوان
   - TaxNumber → الرقم الضريبي
   - CreditLimit → حد الائتمان
   - IsActive → نشط

### النموذج 5: إدارة الوصفات (frm_Recipes)

#### الخطوات:
1. **إنشاء النموذج الرئيسي**:
   - استخدم `Form Wizard`
   - اختر جدول `Recipes`
   - الحقول: RecipeName, Description, YieldQuantity, PreparationTime, IsActive

2. **إنشاء النموذج الفرعي للمكونات**:
   - أنشئ نموذج منفصل لجدول `RecipeIngredients`
   - اربطه بالنموذج الرئيسي عبر RecipeID

3. **التسميات العربية**:
   - RecipeName → اسم الوصفة
   - Description → الوصف
   - YieldQuantity → الكمية المنتجة
   - PreparationTime → وقت التحضير
   - IsActive → نشط

## إنشاء النموذج الرئيسي (frm_MainMenu)

### الخطوات:
1. **إنشاء نموذج فارغ**:
   - اذهب إلى `Create` > `Blank Form`
   - احفظه باسم `frm_MainMenu`

2. **تصميم الواجهة**:
   - أضف عنوان: "نظام إدارة مصنع المواد الغذائية"
   - أضف أزرار للوحدات المختلفة:
     - إدارة الموردين
     - إدارة المواد الخام
     - إدارة المنتجات
     - إدارة العملاء
     - إدارة الوصفات
     - التقارير
     - الإعدادات

3. **برمجة الأزرار**:
   - لكل زر، أضف كود VBA لفتح النموذج المقابل:
   ```vba
   Private Sub btnSuppliers_Click()
       DoCmd.OpenForm "frm_Suppliers"
   End Sub
   
   Private Sub btnRawMaterials_Click()
       DoCmd.OpenForm "frm_RawMaterials"
   End Sub
   
   Private Sub btnProducts_Click()
       DoCmd.OpenForm "frm_Products"
   End Sub
   
   Private Sub btnCustomers_Click()
       DoCmd.OpenForm "frm_Customers"
   End Sub
   
   Private Sub btnRecipes_Click()
       DoCmd.OpenForm "frm_Recipes"
   End Sub
   ```

## تحسينات إضافية

### 1. تنسيق النماذج:
- استخدم خطوط عربية مثل Arial Unicode MS
- اضبط اتجاه النص من اليمين لليسار
- استخدم ألوان متناسقة

### 2. التحقق من صحة البيانات:
- أضف قواعد التحقق للحقول المطلوبة
- أضف رسائل خطأ باللغة العربية

### 3. تحسين تجربة المستخدم:
- أضف أزرار تنقل واضحة
- أضف رسائل تأكيد للعمليات المهمة
- أضف مساعدة سريعة للمستخدمين

## اختبار النماذج

### 1. اختبار الوظائف الأساسية:
- إضافة سجل جديد
- تعديل سجل موجود
- حذف سجل
- البحث والتصفية

### 2. اختبار القوائم المنسدلة:
- تأكد من عمل جميع القوائم المنسدلة
- تحقق من عرض البيانات الصحيحة

### 3. اختبار التنقل:
- تأكد من عمل جميع أزرار التنقل
- اختبر فتح النماذج من النموذج الرئيسي

## نصائح مهمة

1. **احفظ عملك بانتظام** أثناء التصميم
2. **اختبر كل نموذج** قبل الانتقال للتالي
3. **استخدم أسماء واضحة** للنماذج والعناصر
4. **أضف تعليقات** في كود VBA للمرجعية المستقبلية
5. **احتفظ بنسخة احتياطية** من قاعدة البيانات

## الخطوة التالية

بعد إكمال النماذج، يمكنك الانتقال لإنشاء التقارير باستخدام الاستعلامات التي تم إنشاؤها مسبقاً.
